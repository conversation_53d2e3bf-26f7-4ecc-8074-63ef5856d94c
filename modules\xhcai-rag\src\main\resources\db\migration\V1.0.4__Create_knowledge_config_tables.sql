-- 知识库分段配置表
CREATE TABLE IF NOT EXISTS knowledge_segment_config (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    
    -- 分段配置 (JSO<PERSON>)
    segment_config JSONB NOT NULL DEFAULT '{}',
    
    -- 长度限制配置 (J<PERSON><PERSON>)
    limit_config JSONB NOT NULL DEFAULT '{}',
    
    -- 清洗配置 (JSON)
    cleaning_config JSONB NOT NULL DEFAULT '{}',
    
    -- 基础字段
    remark VARCHAR(500),
    deleted BOOLEAN DEFAULT FALSE,
    created_by <PERSON><PERSON><PERSON><PERSON>(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHA<PERSON>(36),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 知识库向量化配置表
CREATE TABLE IF NOT EXISTS knowledge_vectorization_config (
    id VARCHAR(36) PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    
    -- 索引模式：high_quality（高质量）、economy（经济）
    index_mode VARCHAR(50) NOT NULL DEFAULT 'high_quality',
    
    -- 嵌入模型（仅高质量模式）
    embedding_model VARCHAR(100),
    
    -- 检索设置 (JSON)
    retrieval_settings JSONB NOT NULL DEFAULT '{}',
    
    -- 基础字段
    remark VARCHAR(500),
    deleted BOOLEAN DEFAULT FALSE,
    created_by VARCHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(36),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_knowledge_segment_config_update_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_knowledge_vectorization_config_update_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_knowledge_segment_config_update_time ON knowledge_segment_config;
CREATE TRIGGER trigger_knowledge_segment_config_update_time
    BEFORE UPDATE ON knowledge_segment_config
    FOR EACH ROW
    EXECUTE FUNCTION update_knowledge_segment_config_update_time();

DROP TRIGGER IF EXISTS trigger_knowledge_vectorization_config_update_time ON knowledge_vectorization_config;
CREATE TRIGGER trigger_knowledge_vectorization_config_update_time
    BEFORE UPDATE ON knowledge_vectorization_config
    FOR EACH ROW
    EXECUTE FUNCTION update_knowledge_vectorization_config_update_time();

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_knowledge_segment_config_tenant_id ON knowledge_segment_config(tenant_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_segment_config_deleted ON knowledge_segment_config(deleted);
CREATE INDEX IF NOT EXISTS idx_knowledge_segment_config_created_at ON knowledge_segment_config(created_at);

CREATE INDEX IF NOT EXISTS idx_knowledge_vectorization_config_tenant_id ON knowledge_vectorization_config(tenant_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_vectorization_config_deleted ON knowledge_vectorization_config(deleted);
CREATE INDEX IF NOT EXISTS idx_knowledge_vectorization_config_created_at ON knowledge_vectorization_config(created_at);
CREATE INDEX IF NOT EXISTS idx_knowledge_vectorization_config_index_mode ON knowledge_vectorization_config(index_mode);

-- 添加注释
COMMENT ON TABLE knowledge_segment_config IS '知识库分段配置表';
COMMENT ON COLUMN knowledge_segment_config.id IS '主键ID';
COMMENT ON COLUMN knowledge_segment_config.tenant_id IS '租户ID';
COMMENT ON COLUMN knowledge_segment_config.segment_config IS '分段配置JSON';
COMMENT ON COLUMN knowledge_segment_config.limit_config IS '长度限制配置JSON';
COMMENT ON COLUMN knowledge_segment_config.cleaning_config IS '清洗配置JSON';

COMMENT ON TABLE knowledge_vectorization_config IS '知识库向量化配置表';
COMMENT ON COLUMN knowledge_vectorization_config.id IS '主键ID';
COMMENT ON COLUMN knowledge_vectorization_config.tenant_id IS '租户ID';
COMMENT ON COLUMN knowledge_vectorization_config.index_mode IS '索引模式';
COMMENT ON COLUMN knowledge_vectorization_config.embedding_model IS '嵌入模型';
COMMENT ON COLUMN knowledge_vectorization_config.retrieval_settings IS '检索设置JSON';

-- 插入默认配置数据（可选）
-- 这里可以为系统租户插入默认配置
-- INSERT INTO knowledge_segment_config (id, tenant_id, segment_config, limit_config, cleaning_config, created_by)
-- VALUES (
--     gen_random_uuid()::text,
--     'system',
--     '{"type": "natural", "config": {"delimiter": "\\n\\n"}}',
--     '{"maxLen": 1024, "overlapLen": 50}',
--     '{"removeEmptyLines": false, "removeExtraSpaces": false, "removeSpecialChars": false, "normalizeText": false, "deleteSymbol": false, "deleteInlineMedia": false, "filterKeywords": ""}',
--     'system'
-- );

-- INSERT INTO knowledge_vectorization_config (id, tenant_id, index_mode, embedding_model, retrieval_settings, created_by)
-- VALUES (
--     gen_random_uuid()::text,
--     'system',
--     'high_quality',
--     'text-embedding-ada-002',
--     '{"retrievalMode": "hybrid", "enableRerank": false, "rerankModel": "bge-reranker-large", "topK": 5, "scoreThreshold": 0.7, "hybridWeights": {"semanticWeight": 0.7, "keywordWeight": 0.3}}',
--     'system'
-- );
