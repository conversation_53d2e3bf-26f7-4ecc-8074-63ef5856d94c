package com.xhcai.modules.rag.plugins.rabbitmq.model;

/**
 * 消息类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum MessageType {

    /**
     * 文档处理消息
     */
    DOCUMENT_PROCESSING("document.processing", "文档处理", "rag.document.processing"),
    /**
     * 文档分段处理消息
     */
    DOCUMENT_SEGMENTATION("document.segmentation", "文档分段处理", "rag.document.segmentation"),
    /**
     * 文档状态推送消息
     */
    DOCUMENT_STATUS_PUSH("document.status.push", "文档状态推送", "rag.document.status.push"),
    /**
     * 向量化处理消息
     */
    EMBEDDING_PROCESSING("embedding.processing", "向量化处理", "rag.embedding.processing"),
    /**
     * 通知消息
     */
    NOTIFICATION("notification", "通知消息", "rag.notification"),
    /**
     * 系统消息
     */
    SYSTEM("system", "系统消息", "rag.system"),
    /**
     * 错误消息
     */
    ERROR("error", "错误消息", "rag.error"),
    /**
     * 错误消息
     */
    DEAD_LETTER("dead.letter", "死信队列", "rag.dead.letter"),
    /**
     * 健康检查消息
     */
    HEALTH_CHECK("health.check", "健康检查", "rag.health.check");

    /**
     * 路由键
     */
    private final String routingKey;

    /**
     * 描述
     */
    private final String description;

    /**
     * 队列名称
     */
    private final String queueName;

    MessageType(String routingKey, String description, String queueName) {
        this.routingKey = routingKey;
        this.description = description;
        this.queueName = queueName;
    }

    public String getRoutingKey() {
        return routingKey;
    }

    public String getDescription() {
        return description;
    }

    public String getQueueName() {
        return queueName;
    }

    /**
     * 根据路由键获取消息类型
     */
    public static MessageType fromRoutingKey(String routingKey) {
        for (MessageType type : values()) {
            if (type.getRoutingKey().equals(routingKey)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的路由键: " + routingKey);
    }

    /**
     * 根据队列名称获取消息类型
     */
    public static MessageType fromQueueName(String queueName) {
        for (MessageType type : values()) {
            if (type.getQueueName().equals(queueName)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的队列名称: " + queueName);
    }
}
