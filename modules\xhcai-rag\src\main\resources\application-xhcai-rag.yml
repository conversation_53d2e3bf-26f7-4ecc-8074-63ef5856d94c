xhcai:
  # RAG知识库管理模块配置
  rag:
    # 文档处理配置
    document:
      # 最大文件大小
      max-size: ${RAG_DOCUMENT_MAX_SIZE:50MB}
      # 允许的文件类型
      allowed-types: ${RAG_DOCUMENT_ALLOWED_TYPES:pdf,doc,docx,txt,md,html,csv,xlsx,pptx}
      # 分段大小
      chunk-size: ${RAG_DOCUMENT_CHUNK_SIZE:1000}
      # 分段重叠
      chunk-overlap: ${RAG_DOCUMENT_CHUNK_OVERLAP:200}
      # 最大分段数
      max-chunks: ${RAG_DOCUMENT_MAX_CHUNKS:1000}
      # 文档处理超时时间（秒）
      processing-timeout: ${RAG_DOCUMENT_PROCESSING_TIMEOUT:300}
      # 临时文件存储路径
      temp-path: ${RAG_DOCUMENT_TEMP_PATH:./temp/rag/documents}
      # 是否保留原始文件
      keep-original: ${RAG_DOCUMENT_KEEP_ORIGINAL:true}

    # 向量化配置
    embedding:
      # 默认嵌入模型
      model: ${RAG_EMBEDDING_MODEL:text-embedding-ada-002}
      # 向量维度
      dimension: ${RAG_EMBEDDING_DIMENSION:1536}
      # 批处理大小
      batch-size: ${RAG_EMBEDDING_BATCH_SIZE:100}
      # 向量化超时时间（秒）
      timeout: ${RAG_EMBEDDING_TIMEOUT:60}
      # 最大重试次数
      max-retries: ${RAG_EMBEDDING_MAX_RETRIES:3}
      # 重试间隔（毫秒）
      retry-interval: ${RAG_EMBEDDING_RETRY_INTERVAL:1000}
      # 是否启用缓存
      cache-enabled: ${RAG_EMBEDDING_CACHE_ENABLED:true}

    # 检索配置
    retrieval:
      # 默认返回结果数量
      top-k: ${RAG_RETRIEVAL_TOP_K:5}
      # 相似度阈值
      score-threshold: ${RAG_RETRIEVAL_SCORE_THRESHOLD:0.7}
      # 是否启用重排序
      rerank-enabled: ${RAG_RETRIEVAL_RERANK_ENABLED:true}
      # 重排序模型
      rerank-model: ${RAG_RETRIEVAL_RERANK_MODEL:gte-rerank-v2}
      # 检索超时时间（秒）
      timeout: ${RAG_RETRIEVAL_TIMEOUT:30}
      # 最大检索结果数
      max-results: ${RAG_RETRIEVAL_MAX_RESULTS:100}
      # 检索方法 semantic_search, keyword_search, hybrid_search
      search-method: ${RAG_RETRIEVAL_SEARCH_METHOD:semantic_search}

    # 限制配置
    limits:
      # 每个租户最大知识库数量
      max-datasets-per-tenant: ${RAG_LIMITS_MAX_DATASETS_PER_TENANT:100}
      # 每个知识库最大文档数量
      max-documents-per-dataset: ${RAG_LIMITS_MAX_DOCUMENTS_PER_DATASET:1000}
      # 每日最大向量化token数
      max-tokens-per-day: ${RAG_LIMITS_MAX_TOKENS_PER_DAY:1000000}
      # 每日最大检索次数
      max-retrievals-per-day: ${RAG_LIMITS_MAX_RETRIEVALS_PER_DAY:10000}
      # 单个文档最大大小（字节）
      max-document-size: ${RAG_LIMITS_MAX_DOCUMENT_SIZE:52428800}
      # 单次上传最大文件数
      max-upload-files: ${RAG_LIMITS_MAX_UPLOAD_FILES:10}

    # 缓存配置
    cache:
      # 知识库配置缓存
      dataset-config:
        enabled: ${RAG_CACHE_DATASET_CONFIG_ENABLED:true}
        ttl: ${RAG_CACHE_DATASET_CONFIG_TTL:3600}
      # 文档元数据缓存
      document-metadata:
        enabled: ${RAG_CACHE_DOCUMENT_METADATA_ENABLED:true}
        ttl: ${RAG_CACHE_DOCUMENT_METADATA_TTL:1800}
      # 向量缓存
      embedding:
        enabled: ${RAG_CACHE_EMBEDDING_ENABLED:true}
        ttl: ${RAG_CACHE_EMBEDDING_TTL:7200}
      # 检索结果缓存
      retrieval:
        enabled: ${RAG_CACHE_RETRIEVAL_ENABLED:true}
        ttl: ${RAG_CACHE_RETRIEVAL_TTL:300}

    # 任务配置
    task:
      # 文档处理任务
      document-processing:
        # 线程池大小
        thread-pool-size: ${RAG_TASK_DOCUMENT_PROCESSING_THREAD_POOL_SIZE:5}
        # 队列大小
        queue-size: ${RAG_TASK_DOCUMENT_PROCESSING_QUEUE_SIZE:100}
        # 任务超时时间（秒）
        timeout: ${RAG_TASK_DOCUMENT_PROCESSING_TIMEOUT:600}
      # 向量化任务
      embedding:
        # 线程池大小
        thread-pool-size: ${RAG_TASK_EMBEDDING_THREAD_POOL_SIZE:3}
        # 队列大小
        queue-size: ${RAG_TASK_EMBEDDING_QUEUE_SIZE:200}
        # 任务超时时间（秒）
        timeout: ${RAG_TASK_EMBEDDING_TIMEOUT:300}

    # 监控配置
    monitoring:
      # 是否启用监控
      enabled: ${RAG_MONITORING_ENABLED:true}
      # 监控指标收集间隔（秒）
      metrics-interval: ${RAG_MONITORING_METRICS_INTERVAL:60}
      # 性能统计
      performance:
        enabled: ${RAG_MONITORING_PERFORMANCE_ENABLED:true}
        # 慢查询阈值（毫秒）
        slow-query-threshold: ${RAG_MONITORING_PERFORMANCE_SLOW_QUERY_THRESHOLD:1000}

  # Spring AI配置
  spring:
    ai:
      # OpenAI配置
      openai:
        api-key: ${OPENAI_API_KEY:}
        base-url: ${OPENAI_BASE_URL:https://api.openai.com}
        embedding:
          options:
            model: ${RAG_EMBEDDING_MODEL:text-embedding-ada-002}
        chat:
          options:
            model: ${RAG_CHAT_MODEL:gpt-3.5-turbo}
            temperature: ${RAG_CHAT_TEMPERATURE:0.7}
            max-tokens: ${RAG_CHAT_MAX_TOKENS:2000}

      # Ollama配置
      ollama:
        base-url: ${OLLAMA_BASE_URL:http://localhost:11434}
        embedding:
          options:
            model: ${RAG_OLLAMA_EMBEDDING_MODEL:nomic-embed-text}
        chat:
          options:
            model: ${RAG_OLLAMA_CHAT_MODEL:llama2}
            temperature: ${RAG_CHAT_TEMPERATURE:0.7}

      # 向量存储配置
      vectorstore:
        pgvector:
          # 数据库连接配置
          url: ${PGVECTOR_URL:*******************************************}
          username: ${PGVECTOR_USERNAME:postgres}
          password: ${PGVECTOR_PASSWORD:password}
          # 向量表配置
          table-name: ${PGVECTOR_TABLE_NAME:vector_store}
          # 向量维度
          dimensions: ${RAG_EMBEDDING_DIMENSION:1536}
          # 距离函数 COSINE, EUCLIDEAN, DOT_PRODUCT
          distance-type: ${PGVECTOR_DISTANCE_TYPE:COSINE}
          # 是否移除现有数据
          remove-existing-vector-store-table: ${PGVECTOR_REMOVE_EXISTING_TABLE:false}
          # 索引类型
          index-type: ${PGVECTOR_INDEX_TYPE:HNSW}

    # 文件上传配置
    servlet:
      multipart:
        max-file-size: ${RAG_DOCUMENT_MAX_SIZE:50MB}
        max-request-size: ${RAG_DOCUMENT_MAX_REQUEST_SIZE:500MB}
        enabled: true


  # 日志配置
  logging:
    level:
      com.xhcai.modules.rag: ${RAG_LOG_LEVEL:DEBUG}
      org.springframework.ai: ${RAG_LOG_LEVEL:DEBUG}
      org.apache.tika: ${RAG_LOG_LEVEL:INFO}

  # 管理端点配置
  management:
    endpoints:
      web:
        exposure:
          include: health,info,metrics,rag
    endpoint:
      rag:
        enabled: true
    metrics:
      tags:
        module: rag
      export:
        prometheus:
          enabled: ${RAG_MONITORING_PROMETHEUS_ENABLED:true}

  # 定时任务配置
  scheduling:
    enabled: ${RAG_SCHEDULING_ENABLED:true}
    # 清理任务
    cleanup:
      # 清理临时文件的cron表达式
      temp-files-cron: ${RAG_CLEANUP_TEMP_FILES_CRON:0 0 2 * * ?}
      # 清理过期缓存的cron表达式
      expired-cache-cron: ${RAG_CLEANUP_EXPIRED_CACHE_CRON:0 30 1 * * ?}
      # 清理过期向量的cron表达式
      expired-vectors-cron: ${RAG_CLEANUP_EXPIRED_VECTORS_CRON:0 0 3 * * ?}
    # 统计任务
    statistics:
      # 统计数据更新的cron表达式
      update-cron: ${RAG_STATISTICS_UPDATE_CRON:0 0 1 * * ?}
