package com.xhcai.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xhcai.common.datasource.annotation.NoTenant;
import com.xhcai.common.datasource.entity.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 字典类型实体
 *
 * <p>
 * 字典表是全局共享的系统配置，不需要租户隔离，因此使用@NoTenant注解</p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "sys_dict")
@Schema(description = "字典类型")
@TableName("sys_dict")
@NoTenant(reason = "字典表是全局共享的系统配置，不需要租户隔离")
public class SysDict extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 字典名称
     */
    @Column(name = "dict_name", length = 100)
    @Schema(description = "字典名称", example = "用户性别")
    @NotBlank(message = "字典名称不能为空")
    @Size(min = 1, max = 100, message = "字典名称长度必须在1-100个字符之间")
    @TableField("dict_name")
    private String dictName;

    /**
     * 字典类型
     */
    @Column(name = "dict_type", length = 100)
    @Schema(description = "字典类型")
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 1, max = 100, message = "字典类型长度必须在1-100个字符之间")
    @Pattern(regexp = "^[a-z0-9_]+$", message = "字典类型只能包含小写字母、数字和下划线")
    @TableField("dict_type")
    private String dictType;

    /**
     * 状态
     */
    @Column(name = "status", length = 1)
    @Schema(description = "状态", example = "0", allowableValues = {"0", "1"})
    @Pattern(regexp = "^[01]$", message = "状态值必须为0或1")
    @TableField("status")
    private String status;

    /**
     * 是否为系统字典
     */
    @Column(name = "is_system_dict", length = 1)
    @Schema(description = "是否为系统字典", example = "N", allowableValues = {"Y", "N"})
    @Pattern(regexp = "^[YN]$", message = "是否为系统字典值必须为Y或N")
    @TableField("is_system_dict")
    private String isSystemDict = "N";

    @Override
    public String toString() {
        return "SysDict{"
                + "dictName='" + dictName + '\''
                + ", dictType='" + dictType + '\''
                + ", status='" + status + '\''
                + ", isSystemDict='" + isSystemDict + '\''
                + ", " + super.toString()
                + '}';
    }
}
