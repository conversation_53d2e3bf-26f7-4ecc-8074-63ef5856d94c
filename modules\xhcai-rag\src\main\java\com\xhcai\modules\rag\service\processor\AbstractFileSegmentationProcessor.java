package com.xhcai.modules.rag.service.processor;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件分段处理器抽象基类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class AbstractFileSegmentationProcessor implements IFileSegmentationProcessor {
    /**
     * 默认分段大小（字符数）
     */
    public static final int DEFAULT_CHUNK_SIZE = 500000;

    /**
     * 默认重叠大小（字符数）
     */
    public static final int DEFAULT_OVERLAP_SIZE = 0;

    /**
     * 段落分隔符
     */
    protected static final String[] PARAGRAPH_SEPARATORS = {
            "\n\n", "\r\n\r\n", "\n\r\n\r", "。\n", "。\r\n", "！\n", "！\r\n", "？\n", "？\r\n",
            ".\n", ".\r\n", "!\n", "!\r\n", "?\n", "?\r\n"
    };

    /**
     * 句子分隔符
     */
    protected static final String[] SENTENCE_SEPARATORS = {
            "。", "！", "？", ".", "!", "?"
    };

    @Override
    public boolean supports(String fileExtension) {
        return getSupportedFileTypes().contains(fileExtension.toLowerCase());
    }

    @Override
    public int getPriority() {
        return 100; // 默认优先级
    }

    /**
     * 按段落分段文本
     *
     * @param text 原始文本
     * @param mapNatural 分段配置参数
     *     {
     *         "segments": "3"
     *     }
     * @return 分段结果列表
     */
    protected List<SegmentResult> segmentByParagraphs(String text, Map<String, Object> mapNatural) {
        List<SegmentResult> segments = new ArrayList<>();
        int segmentsNumber = MapUtil.getInt(mapNatural, "segments", 3);

        if (text == null || text.trim().isEmpty()) {
            return segments;
        }
        // 按段落分割
        List<String> paragraphs = splitByParagraphs(text, PARAGRAPH_SEPARATORS);

        // 每 segmentsNumber 个段落合并成一个自然段
        List<String> result = new ArrayList<>();
        for (int i = 0; i < paragraphs.size(); i += segmentsNumber) {
            int end = Math.min(i + segmentsNumber, paragraphs.size());
            result.add(String.join(" ", paragraphs.subList(i, end)));
        }
        return segments;
    }

    /**
     * 按指定分隔符分段文本
     * @param text
     * @param mapDelimiter
     *   {
     *       "delimiter": "\n\n"
     *   }
     * @return
     */
    protected List<SegmentResult> segmentByDelimiter(String text, Map<String, Object> mapDelimiter) {
        List<SegmentResult> segments = new ArrayList<>();
        String[] delimiters = {MapUtil.getStr(mapDelimiter, "delimiter", "\n\n")};

        List<String> paragraphs = splitByParagraphs(text, delimiters);
        for (String paragraph : paragraphs) {
            List<String> keywords = extractKeywords(paragraph);
            segments.add(SegmentResult.create(paragraph, segments.size() + 1, keywords));
        }
        return segments;
    }

    /**
     * 按 分隔符 分割文本
     *
     * @param text 原始文本
     * @param paragraphSeparators 分隔符数组
     * @return 段落列表
     */
    private List<String> splitByParagraphs(String text, String[] paragraphSeparators) {
        List<String> paragraphs = new ArrayList<>();

        // 使用多种段落分隔符进行分割
        String[] parts = {text};
        for (String separator : paragraphSeparators) {
            List<String> newParts = new ArrayList<>();
            for (String part : parts) {
                newParts.addAll(Arrays.asList(part.split(Pattern.quote(separator))));
            }
            parts = newParts.toArray(new String[0]);
        }

        for (String part : parts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                paragraphs.add(trimmed);
            }
        }

        return paragraphs;
    }

    /**
     * 按固定大小分段文本
     *
     * @param text 原始文本
     * @param constantLength 分段配置参数
     *     {
     *         "maxLen": "3",
     *         "delimiter": "\n\n",
     *         "overlapLen": 50
     *     }
     * @return 分段结果列表
     */
    protected List<SegmentResult> segmentByFixedSize(String text, Map<String, Object> constantLength) {
        List<SegmentResult> segments = new ArrayList<>();
        int chunkSize = MapUtil.getInt(constantLength, "maxLen", DEFAULT_CHUNK_SIZE);
        int overlapSize = MapUtil.getInt(constantLength, "constantLength", DEFAULT_OVERLAP_SIZE);
        String delimiter = MapUtil.getStr(constantLength, "delimiter", "\n\n");
        
        if (text == null || text.trim().isEmpty()) {
            return segments;
        }

        text = text.trim();
        int position = 1;
        int start = 0;

        while (start < text.length()) {
            int end = Math.min(start + chunkSize, text.length());
            
            // 尝试在句子边界处分割
            if (end < text.length()) {
                int sentenceEnd = findSentenceEnd(text, start, end);
                if (sentenceEnd > start) {
                    end = sentenceEnd;
                }
            }

            String content = text.substring(start, end).trim();
            if (!content.isEmpty()) {
                List<String> keywords = extractKeywords(content);
                segments.add(SegmentResult.create(content, position++, keywords));
            }

            // 计算下一个起始位置，考虑重叠
            start = Math.max(start + 1, end - overlapSize);
        }

        return segments;
    }



    /**
     * 查找句子结束位置
     *
     * @param text  文本
     * @param start 开始位置
     * @param end   结束位置
     * @return 句子结束位置
     */
    private int findSentenceEnd(String text, int start, int end) {
        for (int i = end - 1; i > start; i--) {
            for (String separator : SENTENCE_SEPARATORS) {
                if (text.substring(i).startsWith(separator)) {
                    return i + separator.length();
                }
            }
        }
        return end;
    }

    /**
     * 提取关键字
     *
     * @param content 内容
     * @return 关键字列表
     */
    protected List<String> extractKeywords(String content) {
        List<String> keywords = new ArrayList<>();
        
        if (content == null || content.trim().isEmpty()) {
            return keywords;
        }

        // 简单的关键字提取逻辑
        // 1. 提取中文词汇（2-4个字符）
        Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]{2,4}");
        Matcher chineseMatcher = chinesePattern.matcher(content);
        while (chineseMatcher.find() && keywords.size() < 10) {
            String word = chineseMatcher.group();
            if (!keywords.contains(word)) {
                keywords.add(word);
            }
        }

        // 2. 提取英文单词（3个字符以上）
        Pattern englishPattern = Pattern.compile("\\b[a-zA-Z]{3,}\\b");
        Matcher englishMatcher = englishPattern.matcher(content);
        while (englishMatcher.find() && keywords.size() < 15) {
            String word = englishMatcher.group().toLowerCase();
            if (!keywords.contains(word) && !isStopWord(word)) {
                keywords.add(word);
            }
        }

        return keywords;
    }

    /**
     * 判断是否为停用词
     *
     * @param word 单词
     * @return 是否为停用词
     */
    private boolean isStopWord(String word) {
        String[] stopWords = {
                "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by",
                "from", "up", "about", "into", "through", "during", "before", "after", "above",
                "below", "between", "among", "this", "that", "these", "those", "is", "are", "was",
                "were", "be", "been", "being", "have", "has", "had", "do", "does", "did", "will",
                "would", "could", "should", "may", "might", "must", "can", "shall"
        };
        
        return Arrays.asList(stopWords).contains(word.toLowerCase());
    }

    /**
     * 清理文本内容
     *
     * @param text 原始文本
     * @param cleaningConfig 清洗配置
     * @return 清理后的文本
     */
    protected String cleanText(String text, KnowledgeSegmentConfig.CleaningConfig cleaningConfig) {
        if (text == null) {
            return "";
        }
        if(ObjUtil.isNull(cleaningConfig)){
            // todo 如果没有传分段配置，则使用默认的
            return text.trim();
        }

        if( cleaningConfig.getRemoveEmptyLines() ){
            text = text.replaceAll("\\n\\n+", "\n");
        }
        if( cleaningConfig.getRemoveExtraSpaces() ){
            text = text.replaceAll(" +", " ");
        }
        if( cleaningConfig.getRemoveSpecialChars() ){
            text = text.replaceAll("[\\t\\f]+", "");
        }
        if( cleaningConfig.getNormalizeText() ){
            // todo 文本标准化暂时未开发
//            text.replaceAll("[\\p{Punct}\\p{Space}]+", " ");
        }
        // 删除所有 URL 和电子邮件地址
        if (cleaningConfig.getDeleteSymbol()) {
            text = text.replaceAll("(https?://[\\w./]+)", "");
            text = text.replaceAll("([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})", "");
        }
        if( cleaningConfig.getDeleteInlineMedia() ){
            // todo 删除内嵌多媒体暂时未开发
        }

        // 移除特殊字符（保留基本标点）
        text = text.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");
        
        return text.trim();
    }


    /**
     * 按目录结构进行分段
     * @param catalogItems 目录项列表
     * @return 分段结果列表
     */
    protected List<SegmentResult> segmentByCatalog(List<CatalogItem> catalogItems, KnowledgeSegmentConfig knowledgeSegmentConfig) {
        List<SegmentResult> segments = new ArrayList<>();

        for (CatalogItem item : catalogItems) {
            String content = item.getContent().toString().trim();

            if (content.isEmpty()) {
                continue;
            }

            // 清理内容
            content = cleanText(content, knowledgeSegmentConfig.getCleaningConfig());

            // 创建分段标题
            String segmentTitle = "【" + item.getLevel() + "级标题】" + item.getTitle();
            String fullContent = segmentTitle + "\n\n" + content;

            // 检查是否需要按大小限制进行分段
            if (fullContent.length() > DEFAULT_CHUNK_SIZE) {
                log.debug("目录项内容超过分段大小限制({})，进行进一步分段: title={}, length={}",
                        DEFAULT_CHUNK_SIZE, item.getTitle(), fullContent.length());
                List<SegmentResult> subSegments = segmentCatalogContentBySize(fullContent, segments.size() + 1, item.getTitle());
                segments.addAll(subSegments);
            } else {
                List<String> keywords = extractKeywords(fullContent);
                keywords.add(item.getTitle()); // 添加标题作为关键词
                segments.add(SegmentResult.create(fullContent, segments.size() + 1, keywords));
            }
        }

        return segments;
    }

    /**
     * 按大小限制分段目录内容（遵守DEFAULT_CHUNK_SIZE和DEFAULT_OVERLAP_SIZE）
     * 确保所有内容都被包含，不丢失任何内容
     * @param content 内容
     * @param startPosition 起始位置
     * @param title 标题
     * @return 分段结果列表
     */
    private List<SegmentResult> segmentCatalogContentBySize(String content, int startPosition, String title) {
        List<SegmentResult> segments = new ArrayList<>();

        if (content == null || content.trim().isEmpty()) {
            return segments;
        }

        content = content.trim();
        int position = startPosition;
        int start = 0;

        log.debug("开始按大小分段目录内容: title={}, totalLength={}, chunkSize={}, overlapSize={}",
                title, content.length(), DEFAULT_CHUNK_SIZE, DEFAULT_OVERLAP_SIZE);

        while (start < content.length()) {
            int end = Math.min(start + DEFAULT_CHUNK_SIZE, content.length());

            // 尝试在句子边界处分割，避免截断句子（但不是最后一段时）
            if (end < content.length()) {
                int sentenceEnd = findOptimalSentenceEnd(content, start, end);
                if (sentenceEnd > start + DEFAULT_CHUNK_SIZE / 2) { // 确保分段不会太小
                    end = sentenceEnd;
                }
            }

            String chunkContent = content.substring(start, end).trim();
            if (!chunkContent.isEmpty()) {
                List<String> keywords = extractKeywords(chunkContent);
                keywords.add(title); // 添加原标题作为关键词
                segments.add(SegmentResult.create(chunkContent, position++, keywords));

                log.debug("创建目录分段: position={}, start={}, end={}, length={}, title={}",
                        position - 1, start, end, chunkContent.length(), title);
            }

            // 如果已经处理到文档末尾，结束循环
            if (end >= content.length()) {
                log.debug("已处理到文档末尾，结束分段: end={}, contentLength={}", end, content.length());
                break;
            }

            // 计算下一个起始位置，考虑重叠
            int nextStart = end - DEFAULT_OVERLAP_SIZE;

            // 确保下一个起始位置有效且有新内容
            if (nextStart >= content.length()) {
                log.debug("下一个起始位置超出内容长度，结束分段: nextStart={}, contentLength={}",
                        nextStart, content.length());
                break;
            }

            // 如果剩余内容很少，尝试合并到最后一个分段中，但要检查是否会超出限制
            int remainingLength = content.length() - nextStart;
            if (remainingLength <= DEFAULT_OVERLAP_SIZE && !segments.isEmpty()) {
                log.debug("剩余内容较少({}字符)，尝试合并到最后一个分段", remainingLength);

                // 获取最后一个分段并扩展其内容
                SegmentResult lastSegment = segments.getLast();
                String lastContent = lastSegment.getContent();
                String remainingContent = content.substring(nextStart);

                // 检查是否有重复内容，避免重复添加
                if (!lastContent.contains(remainingContent)) {
                    String mergedContent = lastContent + "\n" + remainingContent;

                    // ✅ 关键修复：检查合并后是否会超出分段大小限制
                    if (mergedContent.length() <= DEFAULT_CHUNK_SIZE) {
                        // 如果不超出限制，才进行合并
                        List<String> keywords = extractKeywords(mergedContent);
                        keywords.add(title);

                        // 替换最后一个分段
                        segments.set(segments.size() - 1,
                                SegmentResult.create(mergedContent, lastSegment.getPosition(), keywords));

                        log.debug("安全合并完成: 最终长度={}", mergedContent.length());
                    } else {
                        // ✅ 如果会超出限制，创建新的分段，确保内容不丢失
                        List<String> keywords = extractKeywords(remainingContent);
                        keywords.add(title);
                        segments.add(SegmentResult.create(remainingContent, position++, keywords));

                        log.debug("创建新分段避免超出限制: 剩余内容长度={}, 新分段长度={}",
                                remainingLength, remainingContent.length());
                    }
                } else {
                    log.debug("剩余内容已包含在最后一个分段中，跳过处理");
                }
                break;
            }

            start = nextStart;
        }

        // 验证内容完整性
        int totalSegmentLength = segments.stream().mapToInt(s -> s.getContent().length()).sum();
        log.debug("分段完成: 原始长度={}, 分段总长度={}, 分段数={}",
                content.length(), totalSegmentLength, segments.size());

        return segments;
    }

    /**
     * 查找最佳的句子结束位置（优化版本，考虑更多分隔符）
     * @param text 文本
     * @param start 开始位置
     * @param end 结束位置
     * @return 最佳句子结束位置
     */
    private int findOptimalSentenceEnd(String text, int start, int end) {
        // 首先尝试在句子分隔符处分割
        int sentenceEnd = findSentenceEndWithSeparators(text, start, end);
        if (sentenceEnd > start) {
            return sentenceEnd;
        }

        // 如果没有找到句子分隔符，尝试在段落分隔符处分割
        int paragraphEnd = findParagraphEnd(text, start, end);
        if (paragraphEnd > start) {
            return paragraphEnd;
        }

        // 如果都没有找到，尝试在空格处分割，避免截断单词
        int spaceEnd = findSpaceEnd(text, start, end);
        if (spaceEnd > start) {
            return spaceEnd;
        }

        // 最后返回原始结束位置
        return end;
    }

    /**
     * 在句子分隔符处查找结束位置
     */
    private int findSentenceEndWithSeparators(String text, int start, int end) {
        // 从后往前查找句子分隔符
        for (int i = end - 1; i > start; i--) {
            for (String separator : SENTENCE_SEPARATORS) {
                if (text.substring(i).startsWith(separator)) {
                    return i + separator.length();
                }
            }
        }
        return start;
    }

    /**
     * 在段落分隔符处查找结束位置
     */
    private int findParagraphEnd(String text, int start, int end) {
        // 从后往前查找段落分隔符
        for (int i = end - 1; i > start; i--) {
            for (String separator : PARAGRAPH_SEPARATORS) {
                if (text.substring(i).startsWith(separator)) {
                    return i + separator.length();
                }
            }
        }
        return start;
    }

    /**
     * 在空格处查找结束位置，避免截断单词
     */
    private int findSpaceEnd(String text, int start, int end) {
        // 从后往前查找空格
        for (int i = end - 1; i > start; i--) {
            if (Character.isWhitespace(text.charAt(i))) {
                return i + 1;
            }
        }
        return start;
    }

    /**
     * 从文本内容检测标题级别（用于DOC文件）
     * @param text 文本内容
     * @return 标题级别，0表示非标题
     */
    protected int detectHeadingLevelFromText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 0;
        }

        text = text.trim();

        // 检查是否以数字编号开头（如：1. 2. 1.1 1.1.1）
        if (text.matches("^\\d+(\\.\\d+)*\\.?\\s+.+")) {
            String[] parts = text.split("\\s+", 2);
            if (parts.length > 0) {
                String numberPart = parts[0].replaceAll("\\.$", "");
                int dotCount = numberPart.split("\\.").length;
                return Math.min(dotCount, 6); // 最多6级标题
            }
        }

        // 检查是否包含"第X章"、"第X节"等模式
        if (text.matches("^第[一二三四五六七八九十\\d]+[章节部分].*")) {
            return 1;
        }

        // 检查是否为简短的标题性文本（通常标题较短且不以句号结尾）
        if (text.length() < 50 && !text.endsWith("。") && !text.endsWith(".")) {
            // 进一步检查是否包含标题关键词
            String[] titleKeywords = {"概述", "简介", "背景", "目的", "范围", "定义", "原则", "要求", "方法", "步骤", "流程", "结论", "总结"};
            for (String keyword : titleKeywords) {
                if (text.contains(keyword)) {
                    return 2; // 默认为2级标题
                }
            }
        }

        return 0;
    }

    /**
     * 目录项内部类
     */
    @Setter
    @Getter
    public static class CatalogItem {
        private String title;           // 标题文本
        private int level;              // 标题级别
        private int startIndex;         // 在文档中的起始位置
        private StringBuilder content;   // 该标题下的内容

        @Override
        public String toString() {
            return "CatalogItem{" +
                    "title='" + title + '\'' +
                    ", level=" + level +
                    ", startIndex=" + startIndex +
                    ", contentLength=" + (content != null ? content.length() : 0) +
                    '}';
        }
    }
}
