package com.xhcai.modules.agent.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.agent.dto.AgentQueryDTO;
import com.xhcai.modules.agent.entity.Agent;
import com.xhcai.modules.agent.vo.AgentVO;

/**
 * 智能体Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent> {

    /**
     * 分页查询智能体列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 智能体列表
     */
    @Select("<script>"
            + "SELECT "
            + "    a.id, a.name, a.description, a.avatar, a.icon_background as iconBackground, a.type, "
            + "    CASE a.type "
            + "        WHEN 'advanced-chat' THEN 'Chatflow' "
            + "        WHEN 'workflow' THEN '工作流' "
            + "        WHEN 'chat' THEN '聊天助手' "
            + "        WHEN 'agent-chat' THEN 'Agent' "
            + "        WHEN 'completion' THEN '文本生成' "
            + "        ELSE '未知' "
            + "    END as typeName, "
            + "    a.model_config as modelConfig, a.system_prompt as systemPrompt, "
            + "    a.tools_config as toolsConfig, a.knowledge_config as knowledgeConfig, "
            + "    a.conversation_config as conversationConfig, a.status, "
            + "    CASE a.status "
            + "        WHEN '1' THEN '启用' "
            + "        WHEN '0' THEN '禁用' "
            + "        ELSE '未知' "
            + "    END as statusName, "
            + "    a.is_public as isPublic, a.sort_order as sortOrder, a.version, "
            + "    a.published_at as publishedAt, a.last_conversation_at as lastConversationAt, "
            + "    a.conversation_count as conversationCount, a.source_type as sourceType, "
            + "    CASE a.source_type "
            + "        WHEN 'platform' THEN '本平台' "
            + "        WHEN 'external' THEN '外部智能体' "
            + "        ELSE '未知' "
            + "    END as sourceTypeName, "
            + "    a.external_agent_id as externalAgentId, a.platform_id as platformId, "
            + "    a.tenant_id as tenantId, a.remark, a.create_by as createBy, a.create_time as createTime, "
            + "    a.update_by as updateBy, a.update_time as updateTime, "
            + "    cu.nickname as createByName, cu.username as createByUsername, "
            + "    cd.dept_name as createByDeptName "
            + "FROM agent a "
            + "LEFT JOIN sys_user cu ON a.create_by = cu.id "
            + "LEFT JOIN sys_dept cd ON cu.dept_id = cd.id "
            + "WHERE a.deleted = 0 "
            + "<if test='query.name != null and query.name != \"\"'>"
            + "    AND a.name LIKE CONCAT('%', #{query.name}, '%') "
            + "</if>"
            + "<if test='query.type != null and query.type != \"\"'>"
            + "    AND a.type = #{query.type} "
            + "</if>"
            + "<if test='query.projectId != null and query.projectId != \"\"'>"
            + "    AND a.project_id = #{query.projectId} "
            + "</if>"
            + "<if test='query.status != null and query.status != \"\"'>"
            + "    AND a.status = #{query.status} "
            + "</if>"
            + "<if test='query.isPublic != null'>"
            + "    AND a.is_public = #{query.isPublic} "
            + "</if>"
            + "<if test='query.createBy != null and query.createBy != \"\"'>"
            + "    AND a.create_by = #{query.createBy} "
            + "</if>"
            + "<if test='query.version != null and query.version != \"\"'>"
            + "    AND a.version = #{query.version} "
            + "</if>"
            + "<if test='query.beginTime != null and query.beginTime != \"\"'>"
            + "    AND a.create_time >= #{query.beginTime} "
            + "</if>"
            + "<if test='query.endTime != null and query.endTime != \"\"'>"
            + "    AND a.create_time &lt;= #{query.endTime} "
            + "</if>"
            + "ORDER BY a.sort_order ASC, a.create_time DESC"
            + "</script>")
    IPage<AgentVO> selectAgentPage(Page<AgentVO> page, @Param("query") AgentQueryDTO queryDTO);

    /**
     * 平台管理员分页查询智能体列表（跨租户）
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 智能体列表
     */
    @Select("<script>"
            + "SELECT "
            + "    a.id, a.name, a.description, a.avatar, a.icon_background as iconBackground, a.type, "
            + "    CASE a.type "
            + "        WHEN 'advanced-chat' THEN 'Chatflow' "
            + "        WHEN 'workflow' THEN '工作流' "
            + "        WHEN 'chat' THEN '聊天助手' "
            + "        WHEN 'agent-chat' THEN 'Agent' "
            + "        WHEN 'completion' THEN '文本生成' "
            + "        ELSE '未知' "
            + "    END as typeName, "
            + "    a.model_config as modelConfig, a.system_prompt as systemPrompt, "
            + "    a.tools_config as toolsConfig, a.knowledge_config as knowledgeConfig, "
            + "    a.conversation_config as conversationConfig, a.status, "
            + "    CASE a.status "
            + "        WHEN '1' THEN '启用' "
            + "        WHEN '0' THEN '禁用' "
            + "        ELSE '未知' "
            + "    END as statusName, "
            + "    a.is_public as isPublic, a.sort_order as sortOrder, a.version, "
            + "    a.published_at as publishedAt, a.last_conversation_at as lastConversationAt, "
            + "    a.conversation_count as conversationCount, a.source_type as sourceType, "
            + "    CASE a.source_type "
            + "        WHEN 'platform' THEN '本平台' "
            + "        WHEN 'external' THEN '外部智能体' "
            + "        ELSE '未知' "
            + "    END as sourceTypeName, "
            + "    a.external_agent_id as externalAgentId, a.platform_id as platformId, "
            + "    a.tenant_id as tenantId, a.remark, a.create_by as createBy, a.create_time as createTime, "
            + "    a.update_by as updateBy, a.update_time as updateTime, "
            + "    cu.nickname as createByName, cu.username as createByUsername, "
            + "    cd.dept_name as createByDeptName, "
            + "    st.tenant_name as tenantName "
            + "FROM agent a "
            + "LEFT JOIN sys_user cu ON a.create_by = cu.id "
            + "LEFT JOIN sys_dept cd ON cu.dept_id = cd.id "
            + "LEFT JOIN sys_tenant st ON a.tenant_id = st.id "
            + "WHERE a.deleted = 0 "
            + "<if test='query.name != null and query.name != \"\"'>"
            + "    AND a.name LIKE CONCAT('%', #{query.name}, '%') "
            + "</if>"
            + "<if test='query.projectId != null and query.projectId != \"\"'>"
            + "    AND a.project_id = #{query.projectId} "
            + "</if>"
            + "<if test='query.type != null and query.type != \"\"'>"
            + "    AND a.type = #{query.type} "
            + "</if>"
            + "<if test='query.status != null and query.status != \"\"'>"
            + "    AND a.status = #{query.status} "
            + "</if>"
            + "<if test='query.isPublic != null'>"
            + "    AND a.is_public = #{query.isPublic} "
            + "</if>"
            + "<if test='query.createBy != null and query.createBy != \"\"'>"
            + "    AND a.create_by = #{query.createBy} "
            + "</if>"
            + "<if test='query.version != null and query.version != \"\"'>"
            + "    AND a.version = #{query.version} "
            + "</if>"
            + "<if test='query.beginTime != null and query.beginTime != \"\"'>"
            + "    AND a.create_time >= #{query.beginTime} "
            + "</if>"
            + "<if test='query.endTime != null and query.endTime != \"\"'>"
            + "    AND a.create_time &lt;= #{query.endTime} "
            + "</if>"
            + "<if test='query.tenantId != null and query.tenantId != \"\"'>"
            + "    AND a.tenant_id = #{query.tenantId} "
            + "</if>"
            + "ORDER BY a.sort_order ASC, a.create_time DESC"
            + "</script>")
    IPage<AgentVO> selectAgentPageForPlatformAdmin(Page<AgentVO> page, @Param("query") AgentQueryDTO queryDTO);

    /**
     * 根据ID查询智能体详情
     *
     * @param id 智能体ID
     * @return 智能体详情
     */
    @Select("SELECT "
            + "    a.id, a.name, a.description, a.avatar, a.type, "
            + "    CASE a.type "
            + "        WHEN 'advanced-chat' THEN 'Chatflow' "
            + "        WHEN 'workflow' THEN '工作流' "
            + "        WHEN 'chat' THEN '聊天助手' "
            + "        WHEN 'agent-chat' THEN 'Agent' "
            + "        WHEN 'completion' THEN '文本生成' "
            + "        ELSE '未知' "
            + "    END as typeName, "
            + "    a.model_config as modelConfig, a.system_prompt as systemPrompt, "
            + "    a.tools_config as toolsConfig, a.knowledge_config as knowledgeConfig, "
            + "    a.conversation_config as conversationConfig, a.status, "
            + "    CASE a.status "
            + "        WHEN '1' THEN '启用' "
            + "        WHEN '0' THEN '禁用' "
            + "        ELSE '未知' "
            + "    END as statusName, "
            + "    a.is_public as isPublic, a.sort_order as sortOrder, a.version, "
            + "    a.published_at as publishedAt, a.last_conversation_at as lastConversationAt, "
            + "    a.conversation_count as conversationCount, a.source_type as sourceType, "
            + "    CASE a.source_type "
            + "        WHEN 'platform' THEN '本平台' "
            + "        WHEN 'external' THEN '外部智能体' "
            + "        ELSE '未知' "
            + "    END as sourceTypeName, "
            + "    a.external_agent_id as externalAgentId, a.platform_id as platformId, "
            + "    a.tenant_id as tenantId, a.remark, a.create_by as createBy, a.create_time as createTime, "
            + "    a.update_by as updateBy, a.update_time as updateTime "
            + "FROM agent a "
            + "WHERE a.id = #{id} AND a.deleted = 0")
    AgentVO selectAgentById(@Param("id") String id);

    /**
     * 查询用户的智能体列表
     *
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 智能体列表
     */
    @Select("SELECT "
            + "    a.id, a.name, a.description, a.avatar, a.type, a.status, "
            + "    a.is_public as isPublic, a.version, a.conversation_count as conversationCount, "
            + "    a.last_conversation_at as lastConversationAt, a.create_time as createTime "
            + "FROM agent a "
            + "WHERE a.create_by = #{userId} AND a.tenant_id = #{tenantId} AND a.deleted = 0 "
            + "ORDER BY a.sort_order ASC, a.create_time DESC")
    List<AgentVO> selectAgentsByUser(@Param("userId") String userId, @Param("tenantId") String tenantId);

    /**
     * 查询公开的智能体列表
     *
     * @param tenantId 租户ID
     * @return 智能体列表
     */
    @Select("SELECT "
            + "    a.id, a.name, a.description, a.avatar, a.type, a.status, a.version, "
            + "    a.conversation_count as conversationCount, a.published_at as publishedAt, "
            + "    a.create_time as createTime "
            + "FROM agent a "
            + "WHERE a.is_public = 1 AND a.status = '1' AND a.tenant_id = #{tenantId} AND a.deleted = 0 "
            + "ORDER BY a.conversation_count DESC, a.published_at DESC")
    List<AgentVO> selectPublicAgents(@Param("tenantId") String tenantId);

    /**
     * 根据类型查询智能体数量
     *
     * @param type 智能体类型
     * @param tenantId 租户ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM agent WHERE type = #{type} AND tenant_id = #{tenantId} AND deleted = 0")
    Long countByType(@Param("type") String type, @Param("tenantId") String tenantId);

    /**
     * 根据状态查询智能体数量
     *
     * @param status 状态
     * @param tenantId 租户ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM agent WHERE status = #{status} AND tenant_id = #{tenantId} AND deleted = 0")
    Long countByStatus(@Param("status") String status, @Param("tenantId") String tenantId);

    /**
     * 更新智能体对话统计信息
     *
     * @param id 智能体ID
     * @param conversationCount 对话次数
     * @param lastConversationAt 最后对话时间
     * @return 更新行数
     */
    @Update("UPDATE agent SET conversation_count = #{conversationCount}, last_conversation_at = #{lastConversationAt}, update_time = NOW() WHERE id = #{id}")
    int updateConversationStats(@Param("id") String id,
            @Param("conversationCount") Long conversationCount,
            @Param("lastConversationAt") LocalDateTime lastConversationAt);

    /**
     * 批量更新智能体状态
     *
     * @param ids 智能体ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新行数
     */
    @Update("<script>"
            + "UPDATE agent SET status = #{status}, update_by = #{updateBy}, update_time = NOW() "
            + "WHERE id IN "
            + "<foreach collection='ids' item='id' open='(' separator=',' close=')'>"
            + "    #{id}"
            + "</foreach>"
            + " AND deleted = 0"
            + "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids,
            @Param("status") String status,
            @Param("updateBy") String updateBy);

    /**
     * 检查智能体名称是否存在
     *
     * @param name 智能体名称
     * @param tenantId 租户ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    @Select("<script>"
            + "SELECT COUNT(*) FROM agent WHERE name = #{name} AND tenant_id = #{tenantId} AND deleted = 0"
            + "<if test='excludeId != null and excludeId != \"\"'>"
            + " AND id != #{excludeId}"
            + "</if>"
            + "</script>")
    Long checkNameExists(@Param("name") String name,
            @Param("tenantId") String tenantId,
            @Param("excludeId") String excludeId);

    /**
     * 获取智能体统计信息
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    @Select("SELECT "
            + "    COUNT(*) as totalCount, "
            + "    SUM(CASE WHEN status = '1' THEN 1 ELSE 0 END) as activeCount, "
            + "    SUM(CASE WHEN type = 'chat' THEN 1 ELSE 0 END) as chatCount, "
            + "    SUM(CASE WHEN type = 'workflow' THEN 1 ELSE 0 END) as workflowCount, "
            + "    SUM(CASE WHEN type = 'completion' THEN 1 ELSE 0 END) as completionCount, "
            + "    SUM(CASE WHEN is_public = 1 THEN 1 ELSE 0 END) as publicCount, "
            + "    SUM(CASE WHEN is_public = 0 THEN 1 ELSE 0 END) as privateCount, "
            + "    (SELECT COALESCE(SUM(ac.message_count), 0) "
            + "     FROM agent_conversation ac "
            + "     WHERE ac.tenant_id = #{tenantId} "
            + "       AND DATE(ac.create_time) = CURDATE() "
            + "       AND ac.deleted = 0) as todayConversationCount, "
            + "    COALESCE(SUM(conversation_count), 0) as totalConversationCount "
            + "FROM agent "
            + "WHERE tenant_id = #{tenantId} AND deleted = 0")
    AgentStatsVO getAgentStats(@Param("tenantId") String tenantId);

    /**
     * 检查第三方智能体是否存在
     */
    @Select("SELECT COUNT(*) FROM third_platform WHERE id = #{externalAgentId}")
    int checkThirdPlatformExists(@Param("externalAgentId") String externalAgentId);

    /**
     * 智能体统计信息VO
     */
    class AgentStatsVO {

        private Long totalCount;
        private Long activeCount;
        private Long chatCount;
        private Long workflowCount;
        private Long completionCount;
        private Long publicCount;
        private Long privateCount;
        private Long todayConversationCount;
        private Long totalConversationCount;

        // Getters and Setters
        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public Long getActiveCount() {
            return activeCount;
        }

        public void setActiveCount(Long activeCount) {
            this.activeCount = activeCount;
        }

        public Long getChatCount() {
            return chatCount;
        }

        public void setChatCount(Long chatCount) {
            this.chatCount = chatCount;
        }

        public Long getWorkflowCount() {
            return workflowCount;
        }

        public void setWorkflowCount(Long workflowCount) {
            this.workflowCount = workflowCount;
        }

        public Long getCompletionCount() {
            return completionCount;
        }

        public void setCompletionCount(Long completionCount) {
            this.completionCount = completionCount;
        }

        public Long getPublicCount() {
            return publicCount;
        }

        public void setPublicCount(Long publicCount) {
            this.publicCount = publicCount;
        }

        public Long getPrivateCount() {
            return privateCount;
        }

        public void setPrivateCount(Long privateCount) {
            this.privateCount = privateCount;
        }

        public Long getTodayConversationCount() {
            return todayConversationCount;
        }

        public void setTodayConversationCount(Long todayConversationCount) {
            this.todayConversationCount = todayConversationCount;
        }

        public Long getTotalConversationCount() {
            return totalConversationCount;
        }

        public void setTotalConversationCount(Long totalConversationCount) {
            this.totalConversationCount = totalConversationCount;
        }
    }

    /**
     * 获取AI探索智能体列表
     *
     * @param tenantId 当前租户ID
     * @param userId 当前用户ID
     * @return AI探索智能体列表
     */
    @Select("SELECT id, name, description, avatar, " +
            "icon_background as iconBackground, type, " +
            "model_config as modelConfig, system_prompt as systemPrompt, " +
            "tools_config as toolsConfig, knowledge_config as knowledgeConfig, " +
            "conversation_config as conversationConfig, status, " +
            "is_public as isPublic, sort_order as sortOrder, " +
            "version, published_at as publishedAt, " +
            "last_conversation_at as lastConversationAt, " +
            "conversation_count as conversationCount, " +
            "source_type as sourceType, external_agent_id as externalAgentId, " +
            "platform_id as platformId, project_id as projectId, " +
            "tenant_id as tenantId, remark, deleted, " +
            "create_by as createBy, create_time as createTime, " +
            "update_by as updateBy, update_time as updateTime " +
            "FROM agent " +
            "WHERE deleted = 0 " +
            "AND ((type = 'advanced-chat' AND tenant_id = #{tenantId} AND create_by = #{userId}) OR is_public = TRUE) " +
            "ORDER BY create_time DESC")
    List<AgentVO> selectAiExploreAgents(@Param("tenantId") String tenantId, @Param("userId") String userId);

    /**
     * 分页查询未关联项目的智能体列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 智能体列表
     */
    @Select("<script>"
            + "SELECT "
            + "    a.id, a.name, a.description, a.avatar, a.icon_background as iconBackground, a.type, "
            + "    CASE a.type "
            + "        WHEN 'advanced-chat' THEN 'Chatflow' "
            + "        WHEN 'workflow' THEN '工作流' "
            + "        WHEN 'chat' THEN '聊天助手' "
            + "        WHEN 'agent-chat' THEN 'Agent' "
            + "        WHEN 'completion' THEN '文本生成' "
            + "        ELSE '未知' "
            + "    END as typeName, "
            + "    a.model_config as modelConfig, a.system_prompt as systemPrompt, "
            + "    a.tools_config as toolsConfig, a.knowledge_config as knowledgeConfig, "
            + "    a.conversation_config as conversationConfig, a.status, "
            + "    CASE a.status "
            + "        WHEN '1' THEN '启用' "
            + "        WHEN '0' THEN '禁用' "
            + "        ELSE '未知' "
            + "    END as statusName, "
            + "    a.is_public as isPublic, a.version, a.conversation_count as conversationCount, "
            + "    a.create_time as createTime, a.update_time as updateTime, "
            + "    a.create_by as createBy, a.update_by as updateBy, a.tenant_id as tenantId "
            + "FROM agent a "
            + "WHERE a.deleted = 0 "
            + "    AND a.tenant_id = #{queryDTO.tenantId} "
            + "    AND (a.project_id IS NULL OR a.project_id = '') "
            + "    <if test='queryDTO.name != null and queryDTO.name != \"\"'>"
            + "        AND a.name LIKE CONCAT('%', #{queryDTO.name}, '%') "
            + "    </if>"
            + "    <if test='queryDTO.type != null and queryDTO.type != \"\"'>"
            + "        AND a.type = #{queryDTO.type} "
            + "    </if>"
            + "    <if test='queryDTO.status != null and queryDTO.status != \"\"'>"
            + "        AND a.status = #{queryDTO.status} "
            + "    </if>"
            + "ORDER BY a.create_time DESC"
            + "</script>")
    IPage<Agent> selectUnlinkedAgents(Page<Agent> page, @Param("queryDTO") AgentQueryDTO queryDTO);
}
