package com.xhcai.modules.dify.service;

import com.xhcai.common.api.response.Result;
import com.xhcai.modules.dify.dto.app.DifyAppParametersResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import reactor.core.publisher.Mono;

/**
 * Dify 应用服务接口
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
public interface IDifyAppService {

    /**
     * 获取已安装应用列表
     *
     * @return 已安装应用列表
     */
    Mono<Result<DifyInstalledAppsResponseDTO>> getInstalledApps();

    /**
     * 获取应用会话参数
     *
     * @param installedAppId 已安装应用ID
     * @return 应用会话参数
     */
    Mono<Result<DifyAppParametersResponseDTO>> getAppParameters(String installedAppId);
}
