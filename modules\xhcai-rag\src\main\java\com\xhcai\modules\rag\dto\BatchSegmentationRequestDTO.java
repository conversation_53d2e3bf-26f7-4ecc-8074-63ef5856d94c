package com.xhcai.modules.rag.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 批量分段处理请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "批量分段处理请求DTO")
@Data
public class BatchSegmentationRequestDTO {

    /**
     * 文档ID列表
     */
    @Schema(description = "文档ID列表", example = "[\"doc1\", \"doc2\"]")
    @NotEmpty(message = "文档ID列表不能为空")
    private List<String> documentIds;

    /**
     * 文档元数据映射 (documentId -> docMetadata)
     */
    @Schema(description = "文档元数据映射")
    @NotNull(message = "文档元数据不能为空")
    private Map<String, Map<String, Object>> docMetadataMap;

    /**
     * 分段配置
     */
    @Schema(description = "分段配置")
    private SegmentationConfig segmentationConfig;

    /**
     * 分段配置内部类
     */
    @Schema(description = "分段配置")
    @Data
    public static class SegmentationConfig {
        
        /**
         * 分段大小
         */
        @Schema(description = "分段大小", example = "1000")
        private Integer chunkSize = 1000;

        /**
         * 重叠大小
         */
        @Schema(description = "重叠大小", example = "200")
        private Integer chunkOverlap = 200;

        /**
         * 分段策略
         */
        @Schema(description = "分段策略", example = "paragraph")
        private String strategy = "paragraph";

        /**
         * 是否保留格式
         */
        @Schema(description = "是否保留格式", example = "true")
        private Boolean preserveFormat = true;

        /**
         * 自定义分隔符
         */
        @Schema(description = "自定义分隔符")
        private List<String> customSeparators;
    }
}
