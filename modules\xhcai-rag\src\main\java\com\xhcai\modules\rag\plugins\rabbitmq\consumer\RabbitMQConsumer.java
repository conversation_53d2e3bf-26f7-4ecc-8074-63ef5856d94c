package com.xhcai.modules.rag.plugins.rabbitmq.consumer;

import com.rabbitmq.client.Channel;
import com.xhcai.modules.rag.plugins.rabbitmq.model.RabbitMQMessage;
import com.xhcai.modules.rag.plugins.rabbitmq.service.MessageProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * RabbitMQ消息消费者 - 工作队列模型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "xhcai.plugin.types.queue.config", name = "type", havingValue = "rabbitmq")
public class RabbitMQConsumer {

    @Autowired
    private MessageProcessingService messageProcessingService;

    /**
     * 消费文档处理消息
     */
    @RabbitListener(queues = "rag.document.processing")
    public void consumeDocumentProcessingMessage(@Payload RabbitMQMessage message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        log.info("接收到文档处理消息: messageId={}, documentId={}",
                message.getMessageId(), message.getPayload());

        try {
            // 设置消息状态为处理中
            message.setProcessing();

            // 处理文档
            boolean success = messageProcessingService.processDocumentMessage(message);

            if (success) {
                // 处理成功，手动确认消息
                message.setSuccess();
                channel.basicAck(deliveryTag, false);
                log.info("文档处理消息处理成功: messageId={}", message.getMessageId());
            } else {
                // 处理失败，拒绝消息并重新入队
                handleMessageFailure(message, deliveryTag, channel, "文档处理失败");
            }

        } catch (Exception e) {
            log.error("文档处理消息处理异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            handleMessageFailure(message, deliveryTag, channel, e.getMessage());
        }
    }

    /**
     * 消费文档分段处理消息
     */
    @RabbitListener(queues = "rag.document.segmentation")
    public void consumeDocumentSegmentationMessage(@Payload RabbitMQMessage message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        log.info("接收到文档分段处理消息: messageId={}, payload={}",
                message.getMessageId(), message.getPayload());

        try {
            // 设置消息状态为处理中
            message.setProcessing();

            // 处理文档分段
            boolean success = messageProcessingService.processDocumentSegmentationMessage(message);

            if (success) {
                // 处理成功，手动确认消息
                message.setSuccess();
                channel.basicAck(deliveryTag, false);
                log.info("文档分段处理消息处理成功: messageId={}", message.getMessageId());
            } else {
                // 处理失败，拒绝消息并重新入队
                handleMessageFailure(message, deliveryTag, channel, "文档分段处理失败");
            }

        } catch (Exception e) {
            log.error("文档分段处理消息处理异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            handleMessageFailure(message, deliveryTag, channel, e.getMessage());
        }
    }

    /**
     * 消费向量化处理消息
     */
    @RabbitListener(queues = "rag.embedding.processing")
    public void consumeEmbeddingProcessingMessage(@Payload RabbitMQMessage message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        log.info("接收到向量化处理消息: messageId={}, segmentId={}",
                message.getMessageId(), message.getPayload());

        try {
            message.setProcessing();

            // 处理向量化
            boolean success = messageProcessingService.processEmbeddingMessage(message);

            if (success) {
                message.setSuccess();
                channel.basicAck(deliveryTag, false);
                log.info("向量化处理消息处理成功: messageId={}", message.getMessageId());
            } else {
                handleMessageFailure(message, deliveryTag, channel, "向量化处理失败");
            }

        } catch (Exception e) {
            log.error("向量化处理消息处理异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            handleMessageFailure(message, deliveryTag, channel, e.getMessage());
        }
    }

    /**
     * 消费文档状态推送消息
     */
    @RabbitListener(queues = "rag.document.status.push")
    public void consumeDocumentStatusPushMessage(@Payload RabbitMQMessage message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        log.info("接收到文档状态推送消息: messageId={}", message.getMessageId());

        try {
            message.setProcessing();

            // 处理文档状态推送
            boolean success = messageProcessingService.processDocumentStatusPushMessage(message);

            if (success) {
                message.setSuccess();
                channel.basicAck(deliveryTag, false);
                log.info("文档状态推送消息处理成功: messageId={}", message.getMessageId());
            } else {
                handleMessageFailure(message, deliveryTag, channel, "文档状态推送处理失败");
            }

        } catch (Exception e) {
            log.error("文档状态推送消息处理异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            handleMessageFailure(message, deliveryTag, channel, e.getMessage());
        }
    }

    /**
     * 消费通知消息
     */
    @RabbitListener(queues = "rag.notification")
    public void consumeNotificationMessage(@Payload RabbitMQMessage message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        log.info("接收到通知消息: messageId={}", message.getMessageId());

        try {
            message.setProcessing();

            // 处理通知
            boolean success = messageProcessingService.processNotificationMessage(message);

            if (success) {
                message.setSuccess();
                channel.basicAck(deliveryTag, false);
                log.info("通知消息处理成功: messageId={}", message.getMessageId());
            } else {
                handleMessageFailure(message, deliveryTag, channel, "通知处理失败");
            }

        } catch (Exception e) {
            log.error("通知消息处理异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            handleMessageFailure(message, deliveryTag, channel, e.getMessage());
        }
    }

    /**
     * 消费死信队列消息
     */
    @RabbitListener(queues = "rag.dead.letter")
    public void consumeDeadLetterMessage(@Payload RabbitMQMessage message,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Channel channel) {
        log.warn("接收到死信消息: messageId={}, type={}, payload={}",
                message.getMessageId(), message.getMessageType(), message.getPayload());

        try {
            // 记录死信消息
            messageProcessingService.processDeadLetterMessage(message);

            // 确认死信消息
            channel.basicAck(deliveryTag, false);
            log.info("死信消息处理完成: messageId={}", message.getMessageId());

        } catch (Exception e) {
            log.error("死信消息处理异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            try {
                // 拒绝死信消息，不重新入队
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioException) {
                log.error("拒绝死信消息失败: messageId={}", message.getMessageId(), ioException);
            }
        }
    }

    /**
     * 处理消息失败
     */
    private void handleMessageFailure(RabbitMQMessage message, long deliveryTag, Channel channel, String errorMessage) {
        try {
            message.incrementRetryCount();
            message.setFailed(errorMessage);

            if (message.isMaxRetryExceeded()) {
                // 超过最大重试次数，拒绝消息并不重新入队（进入死信队列）
                channel.basicNack(deliveryTag, false, false);
                log.warn("消息超过最大重试次数，进入死信队列: messageId={}, retryCount={}",
                        message.getMessageId(), message.getRetryCount());
            } else {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
                log.warn("消息处理失败，重新入队: messageId={}, retryCount={}, error={}",
                        message.getMessageId(), message.getRetryCount(), errorMessage);
            }

        } catch (IOException e) {
            log.error("处理消息失败时发生异常: messageId={}", message.getMessageId(), e);
        }
    }

    /**
     * 检查消息是否过期
     */
    private boolean isMessageExpired(RabbitMQMessage message) {
        if (message.getExpireTime() == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(message.getExpireTime());
    }

    /**
     * 验证消息
     */
    private boolean validateMessage(RabbitMQMessage message) {
        if (message == null) {
            log.error("接收到空消息");
            return false;
        }

        if (message.getMessageId() == null || message.getMessageId().trim().isEmpty()) {
            log.error("消息ID为空");
            return false;
        }

        if (message.getMessageType() == null) {
            log.error("消息类型为空: messageId={}", message.getMessageId());
            return false;
        }

        if (isMessageExpired(message)) {
            log.warn("消息已过期: messageId={}, expireTime={}",
                    message.getMessageId(), message.getExpireTime());
            return false;
        }

        return true;
    }

    /**
     * 通用消息处理方法
     */
    private void processMessage(RabbitMQMessage message, long deliveryTag, Channel channel,
            MessageProcessor processor, String operationType) {
        if (!validateMessage(message)) {
            try {
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException e) {
                log.error("拒绝无效消息失败: messageId={}", message.getMessageId(), e);
            }
            return;
        }

        log.info("开始处理{}消息: messageId={}", operationType, message.getMessageId());

        try {
            message.setProcessing();

            boolean success = processor.process(message);

            if (success) {
                message.setSuccess();
                channel.basicAck(deliveryTag, false);
                log.info("{}消息处理成功: messageId={}", operationType, message.getMessageId());
            } else {
                handleMessageFailure(message, deliveryTag, channel, operationType + "处理失败");
            }

        } catch (Exception e) {
            log.error("{}消息处理异常: messageId={}, error={}",
                    operationType, message.getMessageId(), e.getMessage(), e);
            handleMessageFailure(message, deliveryTag, channel, e.getMessage());
        }
    }

    /**
     * 消息处理器接口
     */
    @FunctionalInterface
    private interface MessageProcessor {

        boolean process(RabbitMQMessage message) throws Exception;
    }
}
