/**
 * 测试第三方智能体创建功能
 */

// 先登录获取token
const login = async () => {
  const loginData = {
    username: "admin",
    password: "123456"
  }

  try {
    console.log('🔐 正在登录...')

    const response = await fetch('http://localhost:8001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(loginData)
    })

    console.log('登录响应状态:', response.status)

    const result = await response.json()
    console.log('登录响应结果:', result)

    if (result.success && result.data) {
      console.log('✅ 登录成功!')
      return result.data.accessToken
    } else {
      console.log('❌ 登录失败:', result.message)
      return null
    }
  } catch (error) {
    console.error('❌ 登录请求失败:', error)
    return null
  }
}

const testCreateThirdPartyAgent = async () => {
  // 先登录获取token
  const token = await login()
  if (!token) {
    console.log('❌ 无法获取访问令牌，测试终止')
    return
  }

  const testData = {
    name: "客服助手",
    mode: "advanced-chat",
    description: "专业的客服助手，能够回答用户问题",
    platformId: "24c9711ddf2992e37f95f7065d4d378a"
  }

  try {
    console.log('🤖 正在创建第三方智能体...')
    console.log('发送请求:', testData)

    const response = await fetch('http://localhost:8001/api/agent/third-party', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testData)
    })

    console.log('响应状态:', response.status)

    const result = await response.json()
    console.log('响应结果:', result)

    if (result.success) {
      console.log('✅ 创建第三方智能体成功!')
      console.log('智能体ID:', result.message)
    } else {
      console.log('❌ 创建第三方智能体失败:', result.message)
    }
  } catch (error) {
    console.error('❌ 请求失败:', error)
  }
}

// 运行测试
testCreateThirdPartyAgent()
