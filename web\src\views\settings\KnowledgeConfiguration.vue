<template>
  <div class="knowledge-config-container">
    <!-- 页面头部 -->
    <div class="config-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="header-title">
            <i class="fas fa-brain"></i>
            知识库配置
          </h2>
          <p class="header-subtitle">配置知识库的默认分段和向量化参数，这些设置将作为新建知识库的默认配置</p>
        </div>
        <div class="header-actions">
          <button
            class="btn btn-primary"
            @click="saveAllConfigs"
            :disabled="saving"
          >
            <i class="fas fa-save" v-if="!saving"></i>
            <i class="fas fa-spinner fa-spin" v-if="saving"></i>
            {{ saving ? '保存中...' : '保存配置' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 配置内容 -->
    <div class="config-content">
      <!-- 文件分段配置 -->
      <div class="config-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-cut"></i>
            文件分段配置
          </h3>
          <p class="section-subtitle">设置文档分段的默认参数</p>
        </div>

        <div class="section-content">
          <!-- 分段类型 -->
          <div class="form-group">
            <label class="form-label">分段类型</label>
            <div class="grid grid-cols-5 gap-3">
              <label class="radio-option" :style="{backgroundColor: segmentConfig.segmentConfig.type === 'natural' ? '#dbeafe' : ''}" @click="changeSegmentType('natural')">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="segmentConfig.segmentConfig.type"
                  value="natural"
                >
                <span class="radio-text">自然文段切分</span>
                <span class="option-desc">按照自然段落进行分段</span>
              </label>
              <label class="radio-option" :style="{backgroundColor: segmentConfig.segmentConfig.type === 'delimiter' ? '#dbeafe' : ''}" @click="changeSegmentType('delimiter')">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="segmentConfig.segmentConfig.type"
                  value="delimiter"
                >
                <span class="radio-text">分隔符切分</span>
                <span class="option-desc">使用指定分隔符进行分段</span>
              </label>
              <label class="radio-option" :style="{backgroundColor: segmentConfig.segmentConfig.type === 'directory' ? '#dbeafe' : ''}" @click="changeSegmentType('directory')">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="segmentConfig.segmentConfig.type"
                  value="directory"
                >
                <span class="radio-text">目录切分</span>
                <span class="option-desc">按照文档目录结构分段</span>
              </label>
              <label class="radio-option" :style="{backgroundColor: segmentConfig.segmentConfig.type === 'constantLength' ? '#dbeafe' : ''}" @click="changeSegmentType('constantLength')">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="segmentConfig.segmentConfig.type"
                  value="constantLength"
                >
                <span class="radio-text">固定长度切分</span>
                <span class="option-desc">按照文档固定长度切分</span>
              </label>
              <label class="radio-option" :style="{backgroundColor: segmentConfig.segmentConfig.type === 'none' ? '#dbeafe' : ''}" @click="changeSegmentType('none')">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="segmentConfig.segmentConfig.type"
                  value="none"
                >
                <span class="radio-text">不切分</span>
                <span class="option-desc">不作任何切分，将整个文档作为一个段落</span>
              </label>
            </div>
          </div>

          <!-- 分隔符配置 -->
          <div class="form-group" v-if="segmentConfig.segmentConfig.type === 'delimiter'">
            <label class="form-label">分隔符</label>
            <select v-model="delimiterConfig.delimiter" class="form-select">
              <option value="\n\n">换行符 (\\n\\n)</option>
              <option value="##">井号 (##)</option>
              <option value="//">双斜杠 (//)</option>
              <option value="@@">双@符号 (@@)</option>
            </select>
          </div>

          <!-- 目录配置 -->
          <div class="form-group" v-if="segmentConfig.segmentConfig.type === 'directory'">
            <div class="input-with-slider">
              <span class="unit-text">目录层级：</span>
              <input
                type="range"
                v-model="directoryConfig.level"
                min="1"
                max="4"
                step="1"
                class="form-slider"
              >
            </div>
            <div class="form-hint">根据指定的目录层级，将文档进行分段，最大层级为4，最小层级为1。当前设置的目录层级为：<span class="text-blue-300 font-extrabold">{{ directoryConfig.level }}</span></div>
          </div>

          <div class="form-group" v-if="segmentConfig.segmentConfig.type === 'natural'">
            <div class="input-with-slider">
              <span class="unit-text">分段数：</span>
              <input
                type="range"
                v-model="naturalConfig.segments"
                min="1"
                max="5"
                step="1"
                class="form-slider"
              >
            </div>
            <div class="form-hint">根据指定的自然分段数，将文档进行分段，最大分段数为5，最小分段数为1。当前设置的分段数为：<span class="text-blue-300 font-extrabold">{{ naturalConfig.segments }}</span></div>
          </div>


          <!-- 长度限制配置 -->
          <div class="form-row" v-if="segmentConfig.segmentConfig.type === 'constantLength'">
            <div class="form-group">
              <label class="form-label">分段最大长度</label>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="constantLengthConfig.maxLen"
                  min="200"
                  max="2000"
                  step="100"
                  class="form-slider"
                >
                <input
                  type="number"
                  v-model="constantLengthConfig.maxLen"
                  min="200"
                  max="2000"
                  class="form-input-number"
                >
                <span class="unit-text">字符</span>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">分段重叠长度</label>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="constantLengthConfig.overlapLen"
                  :min="0"
                  :max="Math.floor(constantLengthConfig.maxLen * 0.5)"
                  step="10"
                  class="form-slider"
                >
                <input
                  type="number"
                  v-model="constantLengthConfig.overlapLen"
                  :min="0"
                  :max="Math.floor(constantLengthConfig.maxLen * 0.5)"
                  class="form-input-number"
                >
                <span class="unit-text">字符</span>
              </div>
            </div>
          </div>

          <!-- 清洗配置 -->
          <div class="form-group border-t border-solid border-gray-200 pt-4">
            <label class="form-label">文本清洗</label>
            <div class="checkbox-group">
              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="segmentConfig.cleaningConfig.removeEmptyLines"
                >
                <span>移除空行</span>
              </label>
              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="segmentConfig.cleaningConfig.removeExtraSpaces"
                >
                <span>移除多余空格</span>
              </label>
              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="segmentConfig.cleaningConfig.removeSpecialChars"
                >
                <span>移除特殊字符</span>
              </label>
              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="segmentConfig.cleaningConfig.normalizeText"
                >
                <span>文本标准化</span>
              </label>
              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="segmentConfig.cleaningConfig.deleteSymbol"
                >
                <span>删除URL和邮箱</span>
              </label>
              <label class="checkbox-option">
                <input
                  type="checkbox"
                  v-model="segmentConfig.cleaningConfig.deleteInlineMedia"
                >
                <span>删除内嵌多媒体</span>
              </label>
            </div>
          </div>

          <!-- 过滤关键词 -->
          <div class="form-group">
            <label class="form-label">过滤关键词</label>
            <textarea
              v-model="segmentConfig.cleaningConfig.filterKeywords"
              placeholder="输入要过滤的关键词，每行一个"
              class="form-textarea"
              rows="3"
            ></textarea>
            <div class="form-hint">包含这些关键词的分段将被过滤掉</div>
          </div>
        </div>
      </div>

      <!-- 向量化配置 -->
      <div class="config-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-vector-square"></i>
            向量化配置
          </h3>
          <p class="section-subtitle">设置向量化和检索的默认参数</p>
        </div>

        <div class="section-content">
          <!-- 索引模式 -->
          <div class="form-group">
            <label class="form-label">索引模式</label>
            <div class="grid grid-cols-2 gap-3">
              <label class="radio-option" :style="{backgroundColor: vectorizationConfig.indexMode === 'high_quality' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.indexMode"
                  value="high_quality"
                >
                <span class="radio-text">
                  <strong>高质量</strong>
                  <span class="badge badge-recommended">推荐</span>
                </span>
                <div class="option-desc">调用嵌入模型处理文档，实现更精确的检索</div>
              </label>
              <label class="radio-option" :style="{backgroundColor: vectorizationConfig.indexMode === 'economy' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.indexMode"
                  value="economy"
                >
                <span class="radio-text">
                  <strong>经济</strong>
                </span>
                <div class="option-desc">使用关键词检索，不消耗tokens但准确性较低</div>
              </label>
            </div>
          </div>

          <!-- 嵌入模型 -->
          <div class="form-group" v-if="vectorizationConfig.indexMode === 'high_quality'">
            <label class="form-label">嵌入模型</label>
            <select v-model="vectorizationConfig.embeddingModel" class="form-select">
              <option v-for="model in models['Embeddings']" :key="model.modelId" :value="model.modelId">{{model.name}}</option>
            </select>
          </div>

          <!-- 向量数据库 -->
          <div class="form-group">
            <label class="form-label">向量数据库</label>
            <div class="flex items-center space-x-2">
              <select v-model="vectorizationConfig.vectorDatabase" class="form-select flex-1">
                <option value="">请选择向量数据库</option>
                <option
                  v-for="db in enabledVectorDatabases"
                  :key="db.id"
                  :value="db.id"
                >
                  {{ db.name }} ({{ db.typeName }})
                </option>
              </select>
              <button
                @click="refreshVectorDatabases"
                class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                title="刷新向量数据库列表"
              >
                🔄
              </button>
            </div>
            <div v-if="vectorizationConfig.vectorDatabase" class="form-hint mt-2">
              <span v-if="selectedVectorDatabase">
                {{ selectedVectorDatabase.icon }} {{ selectedVectorDatabase.description }}
              </span>
            </div>
          </div>

          <!-- 检索模式 -->
          <div class="form-group border-t border-solid border-gray-200 pt-5">
            <label class="form-label">检索模式</label>
            <div class="grid grid-cols-3 gap-3">
              <label class="radio-option" :style="{backgroundColor: vectorizationConfig.retrievalSettings.retrievalMode === 'vector' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.retrievalSettings.retrievalMode"
                  value="vector"
                >
                <span class="radio-text">向量检索</span>
                <div class="option-desc">基于语义相似度的检索</div>
              </label>
              <label class="radio-option" :style="{backgroundColor: vectorizationConfig.retrievalSettings.retrievalMode === 'fulltext' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.retrievalSettings.retrievalMode"
                  value="fulltext"
                >
                <span class="radio-text">全文检索</span>
                <div class="option-desc">基于关键词匹配的检索</div>
              </label>
              <label class="radio-option" :style="{backgroundColor: vectorizationConfig.retrievalSettings.retrievalMode === 'hybrid' ? '#e9f5fa' : ''}">
                <input
                  type="radio"
                  hidden="hidden"
                  v-model="vectorizationConfig.retrievalSettings.retrievalMode"
                  value="hybrid"
                >
                <span class="radio-text">
                  <strong>混合检索</strong>
                  <span class="badge badge-recommended">推荐</span>
                </span>
                <div class="option-desc">结合向量检索和全文检索的优势</div>
              </label>
            </div>
          </div>

          <!-- 检索参数 -->
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Top K</label>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="vectorizationConfig.retrievalSettings.topK"
                  min="1"
                  max="10"
                  step="1"
                  class="form-slider"
                >
                <input
                  type="number"
                  v-model="vectorizationConfig.retrievalSettings.topK"
                  min="1"
                  max="10"
                  class="form-input-number"
                >
              </div>
              <div class="form-hint">返回最相似的文档片段数量</div>
            </div>

            <div class="form-group">
              <label class="form-label">相似度阈值</label>
              <div class="input-with-slider">
                <input
                  type="range"
                  v-model="vectorizationConfig.retrievalSettings.scoreThreshold"
                  min="0"
                  max="1"
                  step="0.1"
                  class="form-slider"
                >
                <input
                  type="number"
                  v-model="vectorizationConfig.retrievalSettings.scoreThreshold"
                  min="0"
                  max="1"
                  step="0.1"
                  class="form-input-number"
                >
              </div>
              <div class="form-hint">文档片段的最低相似度要求</div>
            </div>
          </div>

          <!-- 重排序配置 -->
          <div class="form-group">
            <label class="checkbox-option">
              <input
                type="checkbox"
                v-model="vectorizationConfig.retrievalSettings.enableRerank"
              >
              <span>启用重排序模型</span>
            </label>
            <div class="form-hint">使用重排序模型优化检索结果排序</div>
          </div>

          <div class="form-group" v-if="vectorizationConfig.retrievalSettings.enableRerank">
            <label class="form-label">重排序模型</label>
            <select v-model="vectorizationConfig.retrievalSettings.rerankModel" class="form-select">
              <option v-for="model in models['Reranker']" :key="model.modelId" :value="model.modelId">{{model.name}}</option>
            </select>
          </div>

          <!-- 混合检索权重 -->
          <div class="form-group" v-if="vectorizationConfig.retrievalSettings.retrievalMode === 'hybrid'  && !vectorizationConfig.retrievalSettings.enableRerank">
            <label class="form-label">检索权重配置</label>
            <div class="weight-config">
              <div class="weight-item">
                <div class="flex justify-between items-center">
                  <label class="weight-label">语义权重<span class="text-blue-400 font-bold px-2 py-1">（{{vectorizationConfig.retrievalSettings.hybridWeights.semanticWeight}}）</span></label>
                  <label class="weight-label"><span class="text-blue-400 font-bold px-2 py-1">（{{vectorizationConfig.retrievalSettings.hybridWeights.keywordWeight}}）</span>关键词权重</label>
                </div>

                <div class="grid grid-cols-1">
                  <input
                    type="range"
                    v-model="vectorizationConfig.retrievalSettings.hybridWeights.semanticWeight"
                    min="0"
                    max="1"
                    step="0.1"
                    class="config-slider"
                    @input="updateKeywordWeight"
                  >
                </div>
              </div>
            </div>
            <div class="form-hint">语义权重和关键词权重之和应为1.0</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElNotification } from 'element-plus'
import type { FileCleanSegmentConfig, VectorizationConfig } from '@/types/rag'
import { KnowledgeConfigAPI } from '@/api/knowledgeConfig'
import { VectorDatabaseAPI, type VectorDatabaseVO } from '@/api/vectorDatabase'
import { pickBy, isNil } from 'lodash-es'
import model, { AiModelAPI, type AiModel, type AiModelQueryParams } from '@/api/model'

import { useRagConfigStore } from '@/stores/ragConfigStore'

// 统一状态管理
const configStore = useRagConfigStore()

// 响应式数据
const saving = ref(false)

// 文件分段配置
const segmentConfig = ref<FileCleanSegmentConfig>(configStore.globalConfig)

// 向量化配置
const vectorizationConfig = ref<VectorizationConfig>({
  indexMode: 'high_quality',
  embeddingModel: 'text-embedding-ada-002',
  vectorDatabase: '',
  retrievalSettings: {
    retrievalMode: 'hybrid',
    enableRerank: false,
    rerankModel: 'bge-reranker-large',
    topK: 5,
    scoreThreshold: 0.7,
    hybridWeights: {
      semanticWeight: 0.7,
      keywordWeight: 0.3
    }
  }
})

// 向量数据库相关
const enabledVectorDatabases = ref<VectorDatabaseVO[]>([])
const selectedVectorDatabase = computed(() => {
  return enabledVectorDatabases.value.find(db => db.id === vectorizationConfig.value.vectorDatabase)
})

// 安全访问嵌套配置的计算属性
const delimiterConfig = computed({
  get: () => segmentConfig.value.segmentConfig.delimiter || { delimiter: '\n\n' },
  set: (value: any) => {
    if (!segmentConfig.value.segmentConfig.delimiter) {
      segmentConfig.value.segmentConfig.delimiter = {}
    }
    Object.assign(segmentConfig.value.segmentConfig.delimiter, value)
  }
})

const constantLengthConfig = computed({
  get: () => segmentConfig.value.segmentConfig.constantLength || { maxLen: 1024, overlapLen: 50 },
  set: (value: any) => {
    if (!segmentConfig.value.segmentConfig.constantLength) {
      segmentConfig.value.segmentConfig.constantLength = {}
    }
    Object.assign(segmentConfig.value.segmentConfig.constantLength, value)
  }
})

const directoryConfig = computed({
  get: () => segmentConfig.value.segmentConfig.directory || { level: 3 },
  set: (value: any) => {
    if (!segmentConfig.value.segmentConfig.directory) {
      segmentConfig.value.segmentConfig.directory = {}
    }
    Object.assign(segmentConfig.value.segmentConfig.directory, value)
  }
})

const naturalConfig = computed({
  get: () => segmentConfig.value.segmentConfig.natural || { segments: 3 },
  set: (value: any) => {
    if (!segmentConfig.value.segmentConfig.natural) {
      segmentConfig.value.segmentConfig.natural = {}
    }
    Object.assign(segmentConfig.value.segmentConfig.natural, value)
  }
})

// 权重更新方法
const updateKeywordWeight = () => {
  const semanticWeight = vectorizationConfig.value.retrievalSettings.hybridWeights.semanticWeight
  vectorizationConfig.value.retrievalSettings.hybridWeights.keywordWeight =
    Math.round((1 - semanticWeight) * 10) / 10
}

// 深度合并配置，过滤null值
const mergeConfigWithDefaults = (target: any, source: any) => {
  if (!source) return target

  // 过滤掉null值，但保留false、0、空字符串等有效值
  const filteredSource = pickBy(source, (value) => !isNil(value))

  // 递归处理嵌套对象
  Object.keys(filteredSource).forEach(key => {
    if (typeof filteredSource[key] === 'object' && !Array.isArray(filteredSource[key])) {
      if (target[key] && typeof target[key] === 'object') {
        mergeConfigWithDefaults(target[key], filteredSource[key])
      } else {
        target[key] = { ...filteredSource[key] }
      }
    } else {
      target[key] = filteredSource[key]
    }
  })

  return target
}

// 加载配置
const loadConfigs = async () => {
  try {
    // 加载文件分段配置
    const segmentResponse = await KnowledgeConfigAPI.getSegmentConfig()
    if (segmentResponse.success && segmentResponse.data) {
      // 使用深度合并，过滤null值
      mergeConfigWithDefaults(segmentConfig.value, segmentResponse.data)
    }

    // 加载向量化配置
    const vectorResponse = await KnowledgeConfigAPI.getVectorizationConfig()
    if (vectorResponse.success && vectorResponse.data) {
      // 使用深度合并，过滤null值
      mergeConfigWithDefaults(vectorizationConfig.value, vectorResponse.data)
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElNotification({
      title: '加载失败',
      message: '无法加载知识库配置，请刷新页面重试',
      type: 'error'
    })
  }
}

// 保存所有配置
const saveAllConfigs = async () => {
  if (saving.value) return

  saving.value = true

  try {
    // 保存文件分段配置
    const segmentResponse = await KnowledgeConfigAPI.saveSegmentConfig(segmentConfig.value)
    if (!segmentResponse.success) {
      throw new Error(segmentResponse.message || '保存文件分段配置失败')
    }

    // 保存向量化配置
    const vectorResponse = await KnowledgeConfigAPI.saveVectorizationConfig(vectorizationConfig.value)
    if (!vectorResponse.success) {
      throw new Error(vectorResponse.message || '保存向量化配置失败')
    }

    ElNotification({
      title: '保存成功',
      message: '知识库配置已成功保存',
      type: 'success'
    })
  } catch (error) {
    console.error('保存配置失败:', error)
    ElNotification({
      title: '保存失败',
      message: error instanceof Error ? error.message : '保存配置时发生错误',
      type: 'error'
    })
  } finally {
    saving.value = false
  }
}

// 切分方式选择处理
const changeSegmentType = (type: 'directory' | 'natural' | 'delimiter' | 'constantLength' | 'none') => {
  segmentConfig.value.segmentConfig.type = type
}

// 加载模型列表
const models = ref<Record<string, AiModel[]>>({})
const loadModels = async () => {
  try {
    const response = await AiModelAPI.getAiModelsByType('Embeddings,Reranker')
    if (response.success && response.data) {
      models.value = response.data.reduce( (acc, item) =>  {
        if (!acc[item.type]) {
          acc[item.type] = []
        }
        acc[item.type].push(item)
        return acc
      }, {} as Record<string, AiModel[]>)
    }
  } catch (error) {
    console.error('加载模型列表失败:', error)
  }
}

// 加载启用的向量数据库列表
const loadEnabledVectorDatabases = async () => {
  try {
    const response = await VectorDatabaseAPI.getEnabledVectorDatabases()
    if (response.success && response.data) {
      enabledVectorDatabases.value = response.data

      // 如果没有选择向量数据库且有默认数据库，自动选择默认数据库
      if (!vectorizationConfig.value.vectorDatabase) {
        const defaultDb = enabledVectorDatabases.value.find(db => db.isDefault === 'Y')
        if (defaultDb) {
          vectorizationConfig.value.vectorDatabase = defaultDb.id
        }
      }
    }
  } catch (error) {
    console.error('加载向量数据库列表失败:', error)
  }
}

// 刷新向量数据库列表
const refreshVectorDatabases = async () => {
  await loadEnabledVectorDatabases()
}

// 组件挂载时加载配置
onMounted(async () => {
  await loadConfigs()
  await loadModels()
  await loadEnabledVectorDatabases()
})
</script>

<style scoped>
.knowledge-config-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 页面头部 */
.config-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-left {
  flex: 1;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.header-title i {
  color: #3b82f6;
  font-size: 28px;
}

.header-subtitle {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 配置内容 */
.config-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* 配置区块 */
.config-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 6px 0;
}

.section-title i {
  color: #3b82f6;
  font-size: 20px;
}

.section-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.section-content {
  padding: 24px;
}

/* 表单组件 */
.form-group {
  margin-bottom: 24px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

/* 单选按钮组 */
.radio-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.radio-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.radio-option input[type="radio"] {
  margin-right: 8px;
  margin-top: 2px;
}

.radio-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #374151;
}

.option-desc {
  font-size: 12px;
  color: #6b7280;
  margin-left: 20px;
}

/* 复选框组 */
.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.checkbox-option input[type="checkbox"] {
  margin: 0;
}

.checkbox-option span {
  font-size: 14px;
  color: #374151;
}

/* 表单输入 */
.form-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
  transition: all 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 滑块输入 */
.input-with-slider {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.form-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-input-number {
  width: 80px;
  padding: 8px 10px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  transition: all 0.2s ease;
}

.form-input-number:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.unit-text {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

/* 权重配置 */
.weight-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.weight-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weight-label {
  font-size: 13px;
  font-weight: 500;
  color: #475569;
}

/* 提示文本 */
.form-hint {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

/* 徽章 */
.badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.badge-recommended {
  background: #dbeafe;
  color: #1d4ed8;
  border: 1px solid #bfdbfe;
}

/* 按钮 */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .checkbox-group {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .config-content {
    padding: 16px;
    gap: 24px;
  }

  .section-content {
    padding: 16px;
  }

  .config-header {
    padding: 16px;
  }

  .header-title {
    font-size: 20px;
  }

  .section-title {
    font-size: 16px;
  }
}
</style>
