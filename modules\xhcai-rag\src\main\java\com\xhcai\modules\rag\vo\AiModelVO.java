package com.xhcai.modules.rag.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AI模型VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "AI模型信息")
public class AiModelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模型ID
     */
    @Schema(description = "模型ID")
    private String id;

    // ========== 基本信息 ==========
    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String name;

    /**
     * 模型标识
     */
    @Schema(description = "模型标识")
    private String modelId;

    /**
     * 模型提供商
     */
    @Schema(description = "模型提供商")
    private String provider;

    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private String type;

    /**
     * 推理平台
     */
    @Schema(description = "推理平台")
    private String platform;

    /**
     * 模型提供商字典信息
     */
    @Schema(description = "模型提供商字典信息")
    private com.xhcai.modules.system.vo.SysDictDataVO providerDict;

    /**
     * 模型类型字典信息
     */
    @Schema(description = "模型类型字典信息")
    private com.xhcai.modules.system.vo.SysDictDataVO typeDict;

    /**
     * 推理平台字典信息
     */
    @Schema(description = "推理平台字典信息")
    private com.xhcai.modules.system.vo.SysDictDataVO platformDict;

    /**
     * 模型版本
     */
    @Schema(description = "模型版本")
    private String version;

    /**
     * 模型描述
     */
    @Schema(description = "模型描述")
    private String description;

    /**
     * 状态：0-停用，1-启用
     */
    @Schema(description = "状态")
    private String status;

    // ========== 接口配置 ==========
    /**
     * API端点
     */
    @Schema(description = "API端点")
    private String apiEndpoint;

    /**
     * API密钥（脱敏显示）
     */
    @Schema(description = "API密钥")
    private String apiKey;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private String organizationId;

    /**
     * 请求超时时间（秒）
     */
    @Schema(description = "请求超时时间（秒）")
    private Integer timeout;

    // ========== 模型参数 ==========
    /**
     * 最大Token数
     */
    @Schema(description = "最大Token数")
    private Integer maxTokens;

    /**
     * 温度值
     */
    @Schema(description = "温度值")
    private BigDecimal temperature;

    /**
     * Top P值
     */
    @Schema(description = "Top P值")
    private BigDecimal topP;

    /**
     * 频率惩罚
     */
    @Schema(description = "频率惩罚")
    private BigDecimal frequencyPenalty;

    /**
     * 存在惩罚
     */
    @Schema(description = "存在惩罚")
    private BigDecimal presencePenalty;

    /**
     * 停止序列
     */
    @Schema(description = "停止序列")
    private String stopSequences;

    // ========== 费用配置 ==========
    /**
     * 输入价格（$/1K tokens）
     */
    @Schema(description = "输入价格（$/1K tokens）")
    private BigDecimal inputPrice;

    /**
     * 输出价格（$/1K tokens）
     */
    @Schema(description = "输出价格（$/1K tokens）")
    private BigDecimal outputPrice;

    /**
     * 每分钟请求限制
     */
    @Schema(description = "每分钟请求限制")
    private Integer rpmLimit;

    /**
     * 每分钟Token限制
     */
    @Schema(description = "每分钟Token限制")
    private Integer tpmLimit;

    // ========== 审计字段 ==========
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // ========== Getter and Setter ==========
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getApiEndpoint() {
        return apiEndpoint;
    }

    public void setApiEndpoint(String apiEndpoint) {
        this.apiEndpoint = apiEndpoint;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public BigDecimal getTemperature() {
        return temperature;
    }

    public void setTemperature(BigDecimal temperature) {
        this.temperature = temperature;
    }

    public BigDecimal getTopP() {
        return topP;
    }

    public void setTopP(BigDecimal topP) {
        this.topP = topP;
    }

    public BigDecimal getFrequencyPenalty() {
        return frequencyPenalty;
    }

    public void setFrequencyPenalty(BigDecimal frequencyPenalty) {
        this.frequencyPenalty = frequencyPenalty;
    }

    public BigDecimal getPresencePenalty() {
        return presencePenalty;
    }

    public void setPresencePenalty(BigDecimal presencePenalty) {
        this.presencePenalty = presencePenalty;
    }

    public String getStopSequences() {
        return stopSequences;
    }

    public void setStopSequences(String stopSequences) {
        this.stopSequences = stopSequences;
    }

    public BigDecimal getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(BigDecimal inputPrice) {
        this.inputPrice = inputPrice;
    }

    public BigDecimal getOutputPrice() {
        return outputPrice;
    }

    public void setOutputPrice(BigDecimal outputPrice) {
        this.outputPrice = outputPrice;
    }

    public Integer getRpmLimit() {
        return rpmLimit;
    }

    public void setRpmLimit(Integer rpmLimit) {
        this.rpmLimit = rpmLimit;
    }

    public Integer getTpmLimit() {
        return tpmLimit;
    }

    public void setTpmLimit(Integer tpmLimit) {
        this.tpmLimit = tpmLimit;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public com.xhcai.modules.system.vo.SysDictDataVO getProviderDict() {
        return providerDict;
    }

    public void setProviderDict(com.xhcai.modules.system.vo.SysDictDataVO providerDict) {
        this.providerDict = providerDict;
    }

    public com.xhcai.modules.system.vo.SysDictDataVO getTypeDict() {
        return typeDict;
    }

    public void setTypeDict(com.xhcai.modules.system.vo.SysDictDataVO typeDict) {
        this.typeDict = typeDict;
    }

    public com.xhcai.modules.system.vo.SysDictDataVO getPlatformDict() {
        return platformDict;
    }

    public void setPlatformDict(com.xhcai.modules.system.vo.SysDictDataVO platformDict) {
        this.platformDict = platformDict;
    }

    @Override
    public String toString() {
        return "AiModelVO{"
                + "id='" + id + '\''
                + ", name='" + name + '\''
                + ", modelId='" + modelId + '\''
                + ", provider='" + provider + '\''
                + ", type='" + type + '\''
                + ", version='" + version + '\''
                + ", description='" + description + '\''
                + ", status='" + status + '\''
                + ", apiEndpoint='" + apiEndpoint + '\''
                + ", organizationId='" + organizationId + '\''
                + ", timeout=" + timeout
                + ", maxTokens=" + maxTokens
                + ", temperature=" + temperature
                + ", topP=" + topP
                + ", frequencyPenalty=" + frequencyPenalty
                + ", presencePenalty=" + presencePenalty
                + ", stopSequences='" + stopSequences + '\''
                + ", inputPrice=" + inputPrice
                + ", outputPrice=" + outputPrice
                + ", rpmLimit=" + rpmLimit
                + ", tpmLimit=" + tpmLimit
                + ", tenantId='" + tenantId + '\''
                + ", remark='" + remark + '\''
                + ", createBy='" + createBy + '\''
                + ", createTime=" + createTime
                + ", updateBy='" + updateBy + '\''
                + ", updateTime=" + updateTime
                + '}';
    }
}
