package com.xhcai.modules.rag.plugins.rabbitmq.validation;

import com.xhcai.modules.rag.plugins.rabbitmq.config.RabbitMQConfig;
import com.xhcai.modules.rag.plugins.rabbitmq.config.RabbitMQProperties;
import com.xhcai.modules.rag.plugins.rabbitmq.controller.RabbitMQController;
import com.xhcai.modules.rag.plugins.rabbitmq.producer.RabbitMQProducer;
import com.xhcai.modules.rag.plugins.rabbitmq.util.RabbitMQUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * RabbitMQ集成验证类
 * 用于验证所有组件是否正确配置和加载
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "xhcai.plugin.types.queue.config", name = "type", havingValue = "rabbitmq")
public class RabbitMQValidation {

    @Autowired(required = false)
    private RabbitMQProperties rabbitMQProperties;

    @Autowired(required = false)
    private RabbitMQConfig rabbitMQConfig;

    @Autowired(required = false)
    private RabbitMQController rabbitMQController;

    @Autowired(required = false)
    private RabbitMQProducer rabbitMQProducer;

    @Autowired(required = false)
    private RabbitMQUtil rabbitMQUtil;

    @PostConstruct
    public void validateRabbitMQIntegration() {
        log.info("=== RabbitMQ集成验证开始 ===");

        // 验证配置属性
        validateProperties();

        // 验证配置类
        validateConfig();

        // 验证控制器
        validateController();

        // 验证生产者
        validateProducer();

        // 验证工具类
        validateUtil();

        log.info("=== RabbitMQ集成验证完成 ===");
    }

    private void validateProperties() {
        if (rabbitMQProperties != null) {
            log.info("✅ RabbitMQProperties 配置正确");
            log.info("   - 主机: {}", rabbitMQProperties.getHost());
            log.info("   - 端口: {}", rabbitMQProperties.getPort());
            log.info("   - 用户名: {}", rabbitMQProperties.getUsername());
            log.info("   - 虚拟主机: {}", rabbitMQProperties.getVirtualHost());
            log.info("   - 文档处理队列: {}", rabbitMQProperties.getQueue().getDocumentProcessing());
            log.info("   - 向量化处理队列: {}", rabbitMQProperties.getQueue().getEmbeddingProcessing());
            log.info("   - 通知队列: {}", rabbitMQProperties.getQueue().getNotification());
            log.info("   - 死信队列: {}", rabbitMQProperties.getQueue().getDeadLetter());
        } else {
            log.warn("❌ RabbitMQProperties 未加载");
        }
    }

    private void validateConfig() {
        if (rabbitMQConfig != null) {
            log.info("✅ RabbitMQConfig 配置正确");
        } else {
            log.warn("❌ RabbitMQConfig 未加载");
        }
    }

    private void validateController() {
        if (rabbitMQController != null) {
            log.info("✅ RabbitMQController 加载正确");
        } else {
            log.warn("❌ RabbitMQController 未加载");
        }
    }

    private void validateProducer() {
        if (rabbitMQProducer != null) {
            log.info("✅ RabbitMQProducer 加载正确");
        } else {
            log.warn("❌ RabbitMQProducer 未加载");
        }
    }

    private void validateUtil() {
        if (rabbitMQUtil != null) {
            log.info("✅ RabbitMQUtil 加载正确");
        } else {
            log.warn("❌ RabbitMQUtil 未加载");
        }
    }

    /**
     * 获取验证报告
     */
    public ValidationReport getValidationReport() {
        ValidationReport report = new ValidationReport();
        
        report.setPropertiesLoaded(rabbitMQProperties != null);
        report.setConfigLoaded(rabbitMQConfig != null);
        report.setControllerLoaded(rabbitMQController != null);
        report.setProducerLoaded(rabbitMQProducer != null);
        report.setUtilLoaded(rabbitMQUtil != null);
        
        report.setAllComponentsLoaded(
            report.isPropertiesLoaded() &&
            report.isConfigLoaded() &&
            report.isControllerLoaded() &&
            report.isConsumerLoaded() &&
            report.isProducerLoaded() &&
            report.isUtilLoaded()
        );
        
        return report;
    }

    /**
     * 验证报告
     */
    public static class ValidationReport {
        private boolean propertiesLoaded;
        private boolean configLoaded;
        private boolean controllerLoaded;
        private boolean consumerLoaded;
        private boolean producerLoaded;
        private boolean utilLoaded;
        private boolean allComponentsLoaded;

        // Getters and Setters
        public boolean isPropertiesLoaded() {
            return propertiesLoaded;
        }

        public void setPropertiesLoaded(boolean propertiesLoaded) {
            this.propertiesLoaded = propertiesLoaded;
        }

        public boolean isConfigLoaded() {
            return configLoaded;
        }

        public void setConfigLoaded(boolean configLoaded) {
            this.configLoaded = configLoaded;
        }

        public boolean isControllerLoaded() {
            return controllerLoaded;
        }

        public void setControllerLoaded(boolean controllerLoaded) {
            this.controllerLoaded = controllerLoaded;
        }

        public boolean isConsumerLoaded() {
            return consumerLoaded;
        }

        public void setConsumerLoaded(boolean consumerLoaded) {
            this.consumerLoaded = consumerLoaded;
        }

        public boolean isProducerLoaded() {
            return producerLoaded;
        }

        public void setProducerLoaded(boolean producerLoaded) {
            this.producerLoaded = producerLoaded;
        }

        public boolean isUtilLoaded() {
            return utilLoaded;
        }

        public void setUtilLoaded(boolean utilLoaded) {
            this.utilLoaded = utilLoaded;
        }

        public boolean isAllComponentsLoaded() {
            return allComponentsLoaded;
        }

        public void setAllComponentsLoaded(boolean allComponentsLoaded) {
            this.allComponentsLoaded = allComponentsLoaded;
        }

        @Override
        public String toString() {
            return "ValidationReport{" +
                    "propertiesLoaded=" + propertiesLoaded +
                    ", configLoaded=" + configLoaded +
                    ", controllerLoaded=" + controllerLoaded +
                    ", consumerLoaded=" + consumerLoaded +
                    ", producerLoaded=" + producerLoaded +
                    ", utilLoaded=" + utilLoaded +
                    ", allComponentsLoaded=" + allComponentsLoaded +
                    '}';
        }
    }
}
