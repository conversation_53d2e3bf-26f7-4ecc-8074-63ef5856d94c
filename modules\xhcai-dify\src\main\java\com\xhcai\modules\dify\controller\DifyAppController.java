package com.xhcai.modules.dify.controller;

import com.xhcai.common.api.response.Result;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.dify.dto.app.DifyAppParametersResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.service.IDifyAppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * Dify 应用控制器
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Tag(name = "Dify应用管理", description = "Dify应用服务对接")
@RestController
@RequestMapping("/api/dify/apps")
public class DifyAppController {

    private static final Logger log = LoggerFactory.getLogger(DifyAppController.class);

    @Autowired
    private IDifyAppService difyAppService;

    @Operation(summary = "获取已安装应用列表", description = "获取Dify平台已安装的应用列表")
    @GetMapping("/installed")
    @RequiresPermissions("dify:app:list")
    public Mono<Result<DifyInstalledAppsResponseDTO>> getInstalledApps() {
        log.info("获取已安装应用列表");
        return difyAppService.getInstalledApps();
    }

    @Operation(summary = "获取应用会话参数", description = "获取指定应用的会话参数配置")
    @GetMapping("/{installedAppId}/parameters")
    @RequiresPermissions("dify:app:parameters")
    public Mono<Result<DifyAppParametersResponseDTO>> getAppParameters(
            @Parameter(description = "已安装应用ID", required = true)
            @PathVariable String installedAppId) {
        log.info("获取应用会话参数: installedAppId={}", installedAppId);
        return difyAppService.getAppParameters(installedAppId);
    }
}
