package com.xhcai.modules.dify.constant;

/**
 * Dify模块常量
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public class DifyConstants {

    /**
     * 智能体状态
     */
    public static class AgentStatus {
        public static final String ACTIVE = "active";
        public static final String INACTIVE = "inactive";
    }

    /**
     * 智能体类型
     */
    public static class AgentType {
        public static final String ADVANCED_CHAT = "advanced-chat";
        public static final String WORKFLOW = "workflow";
        public static final String CHAT = "chat";
        public static final String AGENT_CHAT = "agent-chat";
        public static final String COMPLETION = "completion";
    }

    /**
     * 知识库状态
     */
    public static class KnowledgeStatus {
        public static final String ACTIVE = "active";
        public static final String INACTIVE = "inactive";
    }

    /**
     * 知识库权限
     */
    public static class KnowledgePermission {
        public static final String PRIVATE = "private";
        public static final String PUBLIC = "public";
        public static final String TEAM = "team";
    }

    /**
     * 文档类型
     */
    public static class DocumentType {
        public static final String TEXT = "text";
        public static final String FILE = "file";
        public static final String URL = "url";
    }

    /**
     * 文档状态
     */
    public static class DocumentStatus {
        public static final String PROCESSING = "processing";
        public static final String COMPLETED = "completed";
        public static final String FAILED = "failed";
    }

    /**
     * 插件状态
     */
    public static class PluginStatus {
        public static final String ACTIVE = "active";
        public static final String INACTIVE = "inactive";
        public static final String DEPRECATED = "deprecated";
    }

    /**
     * 插件类型
     */
    public static class PluginType {
        public static final String BUILTIN = "builtin";
        public static final String CUSTOM = "custom";
        public static final String THIRD_PARTY = "third_party";
    }

    /**
     * 响应模式
     */
    public static class ResponseMode {
        public static final String BLOCKING = "blocking";
        public static final String STREAMING = "streaming";
    }

    /**
     * 搜索方法
     */
    public static class SearchMethod {
        public static final String VECTOR = "vector";
        public static final String KEYWORD = "keyword";
        public static final String HYBRID = "hybrid";
    }

    /**
     * 分段策略
     */
    public static class ChunkStrategy {
        public static final String AUTOMATIC = "automatic";
        public static final String CUSTOM = "custom";
    }

    /**
     * 认证类型
     */
    public static class AuthType {
        public static final String NONE = "none";
        public static final String API_KEY = "api_key";
        public static final String OAUTH = "oauth";
        public static final String BASIC = "basic";
    }

    /**
     * HTTP头
     */
    public static class Headers {
        public static final String AUTHORIZATION = "Authorization";
        public static final String CONTENT_TYPE = "Content-Type";
        public static final String USER_AGENT = "User-Agent";
    }

    /**
     * API路径
     */
    public static class ApiPath {
        // 智能体相关
        public static final String AGENTS = "/v1/agents";
        public static final String CHAT_MESSAGES = "/v1/chat-messages";
        public static final String MESSAGES = "/v1/messages";
        public static final String CONVERSATIONS = "/v1/conversations";

        // 知识库相关
        public static final String DATASETS = "/v1/datasets";
        public static final String DOCUMENTS = "/v1/datasets/{datasetId}/documents";
        public static final String RETRIEVE = "/v1/datasets/{datasetId}/retrieve";

        // 插件相关
        public static final String PLUGINS = "/v1/plugins";
        public static final String PLUGIN_TOOLS = "/v1/plugins/{pluginId}/tools";

        // 应用相关
        public static final String INSTALLED_APPS = "/console/api/installed-apps";
        public static final String APP_PARAMETERS = "/console/api/installed-apps/{installedAppId}/parameters";

        // 文件相关
        public static final String FILE_UPLOAD = "/console/api/files/upload";
        public static final String REMOTE_FILE_UPLOAD = "/console/api/remote-files/upload";
    }

    /**
     * 默认配置
     */
    public static class Defaults {
        public static final int DEFAULT_PAGE_SIZE = 10;
        public static final int DEFAULT_TOP_K = 5;
        public static final double DEFAULT_SCORE_THRESHOLD = 0.5;
        public static final int DEFAULT_CHUNK_SIZE = 1000;
        public static final int DEFAULT_CHUNK_OVERLAP = 100;
        public static final double DEFAULT_TEMPERATURE = 0.7;
        public static final int DEFAULT_MAX_TOKENS = 2000;
    }

    /**
     * 错误码
     */
    public static class ErrorCode {
        public static final int SUCCESS = 200;
        public static final int BAD_REQUEST = 400;
        public static final int UNAUTHORIZED = 401;
        public static final int FORBIDDEN = 403;
        public static final int NOT_FOUND = 404;
        public static final int TOO_MANY_REQUESTS = 429;
        public static final int INTERNAL_SERVER_ERROR = 500;
        public static final int BAD_GATEWAY = 502;
        public static final int SERVICE_UNAVAILABLE = 503;
        public static final int GATEWAY_TIMEOUT = 504;
    }
}
