package com.xhcai.modules.rag.plugins.rabbitmq.producer;

import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.xhcai.modules.rag.plugins.rabbitmq.config.RabbitMQProperties;
import com.xhcai.modules.rag.plugins.rabbitmq.exception.RabbitMQException;
import com.xhcai.modules.rag.plugins.rabbitmq.model.RabbitMQMessage;
import com.xhcai.modules.rag.plugins.rabbitmq.service.DelayMessageService;

import lombok.extern.slf4j.Slf4j;

/**
 * RabbitMQ消息生产者
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "xhcai.plugin.types.queue.config", name = "type", havingValue = "rabbitmq")
public class RabbitMQProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitMQProperties rabbitMQProperties;

    @Autowired
    private DelayMessageService delayMessageService;

    /**
     * 发送消息
     *
     * @param message 消息对象
     */
    public void sendMessage(RabbitMQMessage message) {
        try {
            // 设置消息状态为已发送
            message.setStatus(RabbitMQMessage.MessageStatus.SENT);

            // 创建关联数据
            CorrelationData correlationData = new CorrelationData(message.getMessageId());

            // 发送消息
            rabbitTemplate.convertAndSend(
                    rabbitMQProperties.getExchange().getMain(),
                    message.getMessageType().getRoutingKey(),
                    message,
                    correlationData
            );

            log.info("消息发送成功: messageId={}, type={}, routingKey={}",
                    message.getMessageId(), message.getMessageType(), message.getMessageType().getRoutingKey());

        } catch (Exception e) {
            log.error("消息发送失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            throw new RabbitMQException("消息发送失败", e);
        }
    }

    /**
     * 发送消息到指定队列
     *
     * @param queueName 队列名称
     * @param message 消息对象
     */
    public void sendMessageToQueue(String queueName, RabbitMQMessage message) {
        try {
            message.setStatus(RabbitMQMessage.MessageStatus.SENT);

            CorrelationData correlationData = new CorrelationData(message.getMessageId());

            rabbitTemplate.convertAndSend(queueName, message, correlationData);

            log.info("消息发送到队列成功: messageId={}, queue={}", message.getMessageId(), queueName);

        } catch (Exception e) {
            log.error("消息发送到队列失败: messageId={}, queue={}, error={}",
                    message.getMessageId(), queueName, e.getMessage(), e);
            throw new RabbitMQException("消息发送到队列失败", e);
        }
    }

    /**
     * 发送文档处理消息
     *
     * @param documentId 文档ID
     * @param tenantId 租户ID
     * @param userId 用户ID
     */
    public void sendDocumentProcessingMessage(String documentId, String tenantId, String userId) {
        RabbitMQMessage message = RabbitMQMessage.createDocumentProcessingMessage(documentId, tenantId, userId);
        sendMessage(message);
        log.info("文档处理消息发送成功: documentId={}, tenantId={}, userId={}", documentId, tenantId, userId);
    }

    /**
     * 发送文档分段处理消息
     *
     * @param segmentationData 分段处理数据
     * @param tenantId 租户ID
     * @param userId 用户ID
     */
    public void sendDocumentSegmentationMessage(Object segmentationData, String tenantId, String userId) {
        RabbitMQMessage message = RabbitMQMessage.createDocumentSegmentationMessage(segmentationData, tenantId, userId);
        sendMessage(message);
        log.info("文档分段处理消息发送成功: tenantId={}, userId={}", tenantId, userId);
    }

    /**
     * 发送向量化处理消息
     *
     * @param segmentId 文档段落ID
     * @param tenantId 租户ID
     * @param userId 用户ID
     */
    public void sendEmbeddingProcessingMessage(String segmentId, String tenantId, String userId) {
        RabbitMQMessage message = RabbitMQMessage.createEmbeddingProcessingMessage(segmentId, tenantId, userId);
        sendMessage(message);
        log.info("向量化处理消息发送成功: segmentId={}, tenantId={}, userId={}", segmentId, tenantId, userId);
    }

    /**
     * 发送通知消息
     *
     * @param notificationData 通知数据
     * @param tenantId 租户ID
     * @param userId 用户ID
     */
    public void sendNotificationMessage(Object notificationData, String tenantId, String userId) {
        RabbitMQMessage message = RabbitMQMessage.createNotificationMessage(notificationData, tenantId, userId);
        sendMessage(message);
        log.info("通知消息发送成功: tenantId={}, userId={}", tenantId, userId);
    }

    /**
     * 发送延迟消息 使用智能延迟消息服务，自动选择最佳实现方式
     *
     * @param message 消息对象
     * @param delayTime 延迟时间（毫秒）
     */
    public void sendDelayMessage(RabbitMQMessage message, long delayTime) {
        try {
            log.info("发送延迟消息: messageId={}, delayTime={}ms, 推荐方式: {}",
                    message.getMessageId(), delayTime,
                    delayMessageService.getRecommendedDelayMethod(delayTime));

            delayMessageService.sendDelayMessageSmart(message, delayTime);

        } catch (Exception e) {
            log.error("延迟消息发送失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            throw new RabbitMQException("延迟消息发送失败", e);
        }
    }

    /**
     * 发送优先级消息
     *
     * @param message 消息对象
     * @param priority 优先级（0-255）
     */
    public void sendPriorityMessage(RabbitMQMessage message, int priority) {
        try {
            message.setStatus(RabbitMQMessage.MessageStatus.SENT);
            message.setPriority(priority);

            CorrelationData correlationData = new CorrelationData(message.getMessageId());

            rabbitTemplate.convertAndSend(
                    rabbitMQProperties.getExchange().getMain(),
                    message.getMessageType().getRoutingKey(),
                    message,
                    messagePostProcessor -> {
                        MessageProperties properties = messagePostProcessor.getMessageProperties();
                        properties.setPriority(priority);
                        return messagePostProcessor;
                    },
                    correlationData
            );

            log.info("优先级消息发送成功: messageId={}, priority={}", message.getMessageId(), priority);

        } catch (Exception e) {
            log.error("优先级消息发送失败: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            throw new RabbitMQException("优先级消息发送失败", e);
        }
    }

    /**
     * 批量发送消息
     *
     * @param messages 消息列表
     */
    public void sendBatchMessages(java.util.List<RabbitMQMessage> messages) {
        for (RabbitMQMessage message : messages) {
            try {
                sendMessage(message);
            } catch (Exception e) {
                log.error("批量发送消息失败: messageId={}, error={}", message.getMessageId(), e.getMessage());
                // 继续发送其他消息
            }
        }
        log.info("批量消息发送完成: 总数={}", messages.size());
    }
}
