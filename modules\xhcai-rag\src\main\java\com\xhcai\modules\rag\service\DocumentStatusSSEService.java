package com.xhcai.modules.rag.service;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.rag.dto.DocumentStatusUpdateDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 文档状态SSE推送服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class DocumentStatusSSEService {

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 存储用户的SSE连接 (tenantId_userId -> List<SseEmitter>)
     */
    private final Map<String, CopyOnWriteArrayList<SseEmitter>> userConnections = new ConcurrentHashMap<>();

    /**
     * SSE连接超时时间 (30分钟)
     */
    private static final long SSE_TIMEOUT = 30 * 60 * 1000L;

    /**
     * 创建SSE连接
     *
     * @return SseEmitter
     */
    public SseEmitter createConnection() {
        String tenantId = SecurityUtils.getCurrentTenantId();
        String userId = SecurityUtils.getCurrentUserId();
        String userKey = tenantId + "_" + userId;

        SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

        // 获取或创建用户连接列表
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.computeIfAbsent(
                userKey, k -> new CopyOnWriteArrayList<>());
        emitters.add(emitter);

        log.info("创建SSE连接: tenantId={}, userId={}, 当前连接数={}",
                tenantId, userId, emitters.size());

        // 设置连接完成和超时的回调
        emitter.onCompletion(() -> {
            emitters.remove(emitter);
            log.info("SSE连接完成: tenantId={}, userId={}, 剩余连接数={}",
                    tenantId, userId, emitters.size());
        });

        emitter.onTimeout(() -> {
            emitters.remove(emitter);
            log.info("SSE连接超时: tenantId={}, userId={}, 剩余连接数={}",
                    tenantId, userId, emitters.size());
        });

        emitter.onError((ex) -> {
            emitters.remove(emitter);
            log.error("SSE连接错误: tenantId={}, userId={}, error={}",
                    tenantId, userId, ex.getMessage(), ex);
        });

        // 发送连接成功消息
        JSONObject data = new JSONObject();
        data.put("message", "SSE连接已建立");
        try {
            emitter.send(SseEmitter.event()
                    .name("connected")
                    .data(data.toJSONString()));
        } catch (IOException e) {
            log.error("发送SSE连接成功消息失败: tenantId={}, userId={}", tenantId, userId, e);
            emitters.remove(emitter);
        }

        return emitter;
    }

    /**
     * 推送文档状态更新
     *
     * @param statusUpdate 状态更新信息
     */
    public void pushDocumentStatusUpdate(DocumentStatusUpdateDTO statusUpdate) {
        String userKey = statusUpdate.getTenantId() + "_" + statusUpdate.getUserId();
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.get(userKey);

        if (emitters == null || emitters.isEmpty()) {
            log.warn("没有找到用户的SSE连接: tenantId={}, userId={}, 当前所有连接: {}",
                    statusUpdate.getTenantId(), statusUpdate.getUserId(), userConnections.keySet());
            return;
        }

        log.info("推送文档状态更新: documentId={}, status={}, tenantId={}, userId={}, 连接数={}",
                statusUpdate.getDocumentId(), statusUpdate.getStatus(),
                statusUpdate.getTenantId(), statusUpdate.getUserId(), emitters.size());

        // 向所有连接推送状态更新
        emitters.removeIf(emitter -> {
            try {
                String jsonData = objectMapper.writeValueAsString(statusUpdate);
                emitter.send(SseEmitter.event()
                        .name("document-status-update")
                        .data(jsonData));
                return false; // 发送成功，保留连接
            } catch (IOException e) {
                log.warn("推送文档状态更新失败，移除连接: tenantId={}, userId={}, error={}",
                        statusUpdate.getTenantId(), statusUpdate.getUserId(), e.getMessage());
                return true; // 发送失败，移除连接
            }
        });
    }

    /**
     * 关闭用户的所有SSE连接
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     */
    public void closeUserConnections(String tenantId, String userId) {
        String userKey = tenantId + "_" + userId;
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.remove(userKey);

        if (emitters != null) {
            log.info("关闭用户SSE连接: tenantId={}, userId={}, 连接数={}",
                    tenantId, userId, emitters.size());

            emitters.forEach(emitter -> {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("关闭SSE连接失败: tenantId={}, userId={}", tenantId, userId, e);
                }
            });
        }
    }

    /**
     * 检查用户是否有活跃的SSE连接
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否有活跃连接
     */
    public boolean hasActiveConnection(String tenantId, String userId) {
        String userKey = tenantId + "_" + userId;
        CopyOnWriteArrayList<SseEmitter> emitters = userConnections.get(userKey);
        boolean hasConnection = emitters != null && !emitters.isEmpty();

        log.info("检查SSE连接状态: tenantId={}, userId={}, userKey={}, hasConnection={}, 连接数={}",
                tenantId, userId, userKey, hasConnection,
                emitters != null ? emitters.size() : 0);

        return hasConnection;
    }

    /**
     * 获取当前连接统计信息
     *
     * @return 连接统计信息
     */
    public Map<String, Integer> getConnectionStats() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        userConnections.forEach((userKey, emitters) -> {
            stats.put(userKey, emitters.size());
        });
        return stats;
    }

    /**
     * 清理所有连接
     */
    public void clearAllConnections() {
        log.info("清理所有SSE连接，当前用户数: {}", userConnections.size());

        userConnections.forEach((userKey, emitters) -> {
            emitters.forEach(emitter -> {
                try {
                    emitter.complete();
                } catch (Exception e) {
                    log.warn("关闭SSE连接失败: userKey={}", userKey, e);
                }
            });
        });

        userConnections.clear();
        log.info("所有SSE连接已清理完成");
    }
}
