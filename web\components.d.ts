/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddUserToDeptModal: typeof import('./src/components/settings/unit/AddUserToDeptModal.vue')['default']
    AgentApiManagement: typeof import('./src/components/settings/AgentApiManagement.vue')['default']
    AgentInfoDisplay: typeof import('./src/views/aiExplore/AgentInfoDisplay.vue')['default']
    AgentMonitor: typeof import('./src/views/monitor/AgentMonitor.vue')['default']
    AgentRunnerManager: typeof import('./src/views/agent/AgentRunnerManager.vue')['default']
    AgentRunnerWindow: typeof import('./src/views/agent/AgentRunnerWindow.vue')['default']
    AlertMonitor: typeof import('./src/views/monitor/AlertMonitor.vue')['default']
    AnnouncementManagement: typeof import('./src/components/settings/AnnouncementManagement.vue')['default']
    ApiKeyModal: typeof import('./src/views/agent/ApiKeyModal.vue')['default']
    AssociateThirdPlatform: typeof import('./src/components/agent/AssociateThirdPlatform.vue')['default']
    AudioPlayer: typeof import('./src/views/aiExplore/renderers/AudioPlayer.vue')['default']
    AuditMonitor: typeof import('./src/views/monitor/AuditMonitor.vue')['default']
    BaseFileViewer: typeof import('./src/views/rag/viewers/BaseFileViewer.vue')['default']
    CategoryTreeNode: typeof import('./src/views/rag/components/CategoryTreeNode.vue')['default']
    ChartRenderer: typeof import('./src/views/aiExplore/renderers/ChartRenderer.vue')['default']
    ChatInputArea: typeof import('./src/views/aiExplore/ChatInputArea.vue')['default']
    ConversationSidebar: typeof import('./src/views/aiExplore/ConversationSidebar.vue')['default']
    CreateAgentModal: typeof import('./src/views/agent/CreateAgentModal.vue')['default']
    DeptModal: typeof import('./src/components/settings/unit/DeptModal.vue')['default']
    DeptTreeNode: typeof import('./src/components/settings/DeptTreeNode.vue')['default']
    DeptTreeSelector: typeof import('./src/components/common/selectors/DeptTreeSelector.vue')['default']
    DeptTreeSelectorContent: typeof import('./src/components/common/selectors/DeptTreeSelectorContent.vue')['default']
    DeptUsersDrawer: typeof import('./src/components/settings/unit/DeptUsersDrawer.vue')['default']
    DocumentImportDialog: typeof import('./src/components/DocumentImportDialog.vue')['default']
    DropdownSelector: typeof import('./src/components/common/selectors/DropdownSelector.vue')['default']
    EditAgentModal: typeof import('./src/views/agent/EditAgentModal.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDeptTreeSelector: typeof import('./src/components/common/el-selectors/ElDeptTreeSelector.vue')['default']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLoadingSpinner: typeof import('element-plus/es')['ElLoadingSpinner']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPermissionByRoleSelector: typeof import('./src/components/common/el-selectors/ElPermissionByRoleSelector.vue')['default']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRoleSelector: typeof import('./src/components/common/el-selectors/ElRoleSelector.vue')['default']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStatusSelector: typeof import('./src/components/common/el-selectors/ElStatusSelector.vue')['default']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElUserByDeptSelector: typeof import('./src/components/common/el-selectors/ElUserByDeptSelector.vue')['default']
    ElUserByRoleSelector: typeof import('./src/components/common/el-selectors/ElUserByRoleSelector.vue')['default']
    ExcelViewer: typeof import('./src/views/rag/viewers/ExcelViewer.vue')['default']
    ExploreDialogModal: typeof import('./src/views/aiExplore/ExploreDialogModal.vue')['default']
    ExternalAgentTooltip: typeof import('./src/views/agent/ExternalAgentTooltip.vue')['default']
    FileCategoryPanel: typeof import('./src/views/rag/components/FileCategoryPanel.vue')['default']
    FileListManager: typeof import('./src/views/rag/components/FileListManager.vue')['default']
    FilePermissionPanel: typeof import('./src/views/rag/components/FilePermissionPanel.vue')['default']
    FileRenderer: typeof import('./src/views/aiExplore/renderers/FileRenderer.vue')['default']
    FileSegmentPanel: typeof import('./src/views/rag/datasource/file/FileSegmentPanel.vue')['default']
    FileUploadMenu: typeof import('./src/components/FileUploadMenu.vue')['default']
    FileViewer: typeof import('./src/views/rag/viewers/FileViewer.vue')['default']
    FlowchartRenderer: typeof import('./src/views/aiExplore/renderers/FlowchartRenderer.vue')['default']
    GlobalAgentRunner: typeof import('./src/views/agent/GlobalAgentRunner.vue')['default']
    HelpDocManagement: typeof import('./src/components/settings/HelpDocManagement.vue')['default']
    HelpModal: typeof import('./src/components/HelpModal.vue')['default']
    HoverTooltip: typeof import('./src/components/common/HoverTooltip.vue')['default']
    IconSelector: typeof import('./src/components/common/IconSelector.vue')['default']
    ImageGallery: typeof import('./src/views/aiExplore/renderers/ImageGallery.vue')['default']
    ImageViewer: typeof import('./src/views/rag/viewers/ImageViewer.vue')['default']
    KnowledgeApiManagement: typeof import('./src/components/settings/KnowledgeApiManagement.vue')['default']
    KnowledgeConfig: typeof import('./src/views/rag/components/KnowledgeConfig.vue')['default']
    KnowledgeMonitor: typeof import('./src/views/monitor/KnowledgeMonitor.vue')['default']
    MarkdownRenderer: typeof import('./src/views/aiExplore/renderers/MarkdownRenderer.vue')['default']
    MessageContent: typeof import('./src/views/aiExplore/MessageContent.vue')['default']
    MessageList: typeof import('./src/views/aiExplore/MessageList.vue')['default']
    MinimizedTaskbar: typeof import('./src/views/agent/MinimizedTaskbar.vue')['default']
    ModelAgentSelector: typeof import('./src/views/aiExplore/ModelAgentSelector.vue')['default']
    ModelConfiguration: typeof import('./src/components/settings/ModelConfiguration.vue')['default']
    ModelMonitor: typeof import('./src/views/monitor/ModelMonitor.vue')['default']
    ModelSelector: typeof import('./src/views/aiExplore/ModelSelector.vue')['default']
    ModuleInitPanel: typeof import('./src/views/settings/tenant/ModuleInitPanel.vue')['default']
    Pagination: typeof import('./src/components/common/Pagination.vue')['default']
    PdfViewer: typeof import('./src/views/rag/viewers/PdfViewer.vue')['default']
    PermissionByRoleSelector: typeof import('./src/components/common/selectors/PermissionByRoleSelector.vue')['default']
    PermissionByRoleSelectorContent: typeof import('./src/components/common/selectors/PermissionByRoleSelectorContent.vue')['default']
    PlainTextRenderer: typeof import('./src/views/aiExplore/renderers/PlainTextRenderer.vue')['default']
    PluginMonitor: typeof import('./src/views/monitor/PluginMonitor.vue')['default']
    ProcessSteps: typeof import('./src/views/rag/components/ProcessSteps.vue')['default']
    ProfileModal: typeof import('./src/views/aiExplore/ProfileModal.vue')['default']
    PublishModal: typeof import('./src/views/agent/PublishModal.vue')['default']
    RagConfigTest: typeof import('./src/components/test/RagConfigTest.vue')['default']
    RendererSelector: typeof import('./src/components/RendererSelector.vue')['default']
    RichTextEditor: typeof import('./src/components/RichTextEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RunnerTaskbar: typeof import('./src/components/common/runner-window/RunnerTaskbar.vue')['default']
    RunnerWindow: typeof import('./src/components/common/runner-window/RunnerWindow.vue')['default']
    ServerMonitor: typeof import('./src/views/monitor/ServerMonitor.vue')['default']
    ShareCloneModal: typeof import('./src/views/agent/ShareCloneModal.vue')['default']
    StatusSelector: typeof import('./src/components/common/selectors/StatusSelector.vue')['default']
    StatusSelectorContent: typeof import('./src/components/common/selectors/StatusSelectorContent.vue')['default']
    SystemSettings: typeof import('./src/components/settings/SystemSettings.vue')['default']
    TagEditor: typeof import('./src/views/agent/TagEditor.vue')['default']
    TaskManagement: typeof import('./src/views/rag/components/TaskManagement.vue')['default']
    TenantDetail: typeof import('./src/views/settings/tenant/TenantDetail.vue')['default']
    TenantFilters: typeof import('./src/views/settings/tenant/TenantFilters.vue')['default']
    TenantForm: typeof import('./src/views/settings/tenant/TenantForm.vue')['default']
    TenantManagement: typeof import('./src/views/settings/tenant/TenantManagement.vue')['default']
    TenantManagementNew: typeof import('./src/components/settings/TenantManagementNew.vue')['default']
    TenantManagementRefactored: typeof import('./src/components/settings/TenantManagementRefactored.vue')['default']
    TenantTable: typeof import('./src/views/settings/tenant/TenantTable.vue')['default']
    TextViewer: typeof import('./src/views/rag/viewers/TextViewer.vue')['default']
    ThinkingBlock: typeof import('./src/views/aiExplore/ThinkingBlock.vue')['default']
    ToolsMenu: typeof import('./src/views/aiExplore/ToolsMenu.vue')['default']
    UnitManagement: typeof import('./src/components/settings/UnitManagement.vue')['default']
    UserByDeptSelector: typeof import('./src/components/common/selectors/UserByDeptSelector.vue')['default']
    UserByDeptSelectorContent: typeof import('./src/components/common/selectors/UserByDeptSelectorContent.vue')['default']
    UserByRoleSelector: typeof import('./src/components/common/selectors/UserByRoleSelector.vue')['default']
    UserByRoleSelectorContent: typeof import('./src/components/common/selectors/UserByRoleSelectorContent.vue')['default']
    UserManagement: typeof import('./src/components/settings/UserManagement.vue')['default']
    VideoPlayer: typeof import('./src/views/aiExplore/renderers/VideoPlayer.vue')['default']
    VoiceDialogModal: typeof import('./src/views/aiExplore/VoiceDialogModal.vue')['default']
    WelcomePage: typeof import('./src/views/aiExplore/WelcomePage.vue')['default']
    WordViewer: typeof import('./src/views/rag/viewers/WordViewer.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
