package com.xhcai.modules.rag.plugins.rabbitmq.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.ConditionalRejectingErrorHandler;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.retry.MessageRecoverer;
import org.springframework.amqp.rabbit.retry.RepublishMessageRecoverer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.ErrorHandler;

import com.xhcai.modules.rag.plugins.rabbitmq.model.MessageType;

import lombok.extern.slf4j.Slf4j;

/**
 * RabbitMQ配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@ConditionalOnProperty(prefix = "xhcai.plugin.types.queue.config", name = "type", havingValue = "rabbitmq")
public class RabbitMQConfig {

    @Autowired
    private RabbitMQProperties rabbitMQProperties;

    /**
     * 连接工厂
     */
    @Bean
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(rabbitMQProperties.getHost());
        connectionFactory.setPort(rabbitMQProperties.getPort());
        connectionFactory.setUsername(rabbitMQProperties.getUsername());
        connectionFactory.setPassword(rabbitMQProperties.getPassword());
        connectionFactory.setVirtualHost(rabbitMQProperties.getVirtualHost());
        connectionFactory.setConnectionTimeout(rabbitMQProperties.getConnectionTimeout());
        connectionFactory.setRequestedHeartBeat(rabbitMQProperties.getRequestedHeartbeat());
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(rabbitMQProperties.getPublisherReturns());

        log.info("RabbitMQ连接工厂配置完成: {}:{}", rabbitMQProperties.getHost(), rabbitMQProperties.getPort());
        return connectionFactory;
    }

    /**
     * 消息转换器
     */
    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * RabbitTemplate
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory, MessageConverter messageConverter) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(messageConverter);
        rabbitTemplate.setMandatory(rabbitMQProperties.getMandatory());

        // 发布确认回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.debug("消息发送成功: {}", correlationData);
            } else {
                log.error("消息发送失败: {}, 原因: {}", correlationData, cause);
            }
        });

        // 发布返回回调
        rabbitTemplate.setReturnsCallback(returned -> {
            log.error("消息路由失败: {}, 交换机: {}, 路由键: {}, 响应码: {}, 响应消息: {}",
                    returned.getMessage(), returned.getExchange(), returned.getRoutingKey(),
                    returned.getReplyCode(), returned.getReplyText());
        });

        log.info("RabbitTemplate配置完成");
        return rabbitTemplate;
    }

    /**
     * RabbitAdmin
     */
    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(true);
        log.info("RabbitAdmin配置完成");
        return rabbitAdmin;
    }

    /**
     * 初始化队列和交换机
     */
    @Bean
    public RabbitMQInitializer rabbitMQInitializer(RabbitAdmin rabbitAdmin) {
        return new RabbitMQInitializer(rabbitAdmin);
    }

    /**
     * RabbitMQ初始化器
     */
    public class RabbitMQInitializer {

        private final RabbitAdmin rabbitAdmin;

        public RabbitMQInitializer(RabbitAdmin rabbitAdmin) {
            this.rabbitAdmin = rabbitAdmin;
            initializeQueuesAndExchanges();
        }

        private void initializeQueuesAndExchanges() {
            try {
                log.info("开始初始化RabbitMQ队列和交换机...");

                // 声明交换机
                rabbitAdmin.declareExchange(mainExchange());
                rabbitAdmin.declareExchange(deadLetterExchange());

                // 声明队列
                rabbitAdmin.declareQueue(documentProcessingQueue());
                rabbitAdmin.declareQueue(documentSegmentationQueue());
                rabbitAdmin.declareQueue(documentStatusPushQueue());
                rabbitAdmin.declareQueue(embeddingProcessingQueue());
                rabbitAdmin.declareQueue(notificationQueue());
                rabbitAdmin.declareQueue(deadLetterQueue());

                // 声明绑定
                rabbitAdmin.declareBinding(documentProcessingBinding());
                rabbitAdmin.declareBinding(documentSegmentationBinding());
                rabbitAdmin.declareBinding(documentStatusPushBinding());
                rabbitAdmin.declareBinding(embeddingProcessingBinding());
                rabbitAdmin.declareBinding(notificationBinding());
                rabbitAdmin.declareBinding(deadLetterBinding());

                log.info("RabbitMQ队列和交换机初始化完成");
            } catch (Exception e) {
                log.error("RabbitMQ队列和交换机初始化失败: {}", e.getMessage(), e);
                throw new RuntimeException("RabbitMQ初始化失败", e);
            }
        }
    }

    /**
     * 监听器容器工厂
     */
    @Bean
    @DependsOn("rabbitMQInitializer")
    public RabbitListenerContainerFactory<?> rabbitListenerContainerFactory(ConnectionFactory connectionFactory,
            MessageConverter messageConverter) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);
        factory.setConcurrentConsumers(rabbitMQProperties.getConcurrency());
        factory.setMaxConcurrentConsumers(rabbitMQProperties.getMaxConcurrency());
        factory.setPrefetchCount(rabbitMQProperties.getPrefetchCount());
        factory.setAcknowledgeMode(AcknowledgeMode.valueOf(rabbitMQProperties.getAcknowledgeMode().toUpperCase()));

        // 配置重试和错误处理
        if (rabbitMQProperties.getRetry().getEnabled()) {
            factory.setRetryTemplate(retryTemplate());
            factory.setErrorHandler(errorHandler());
        }

        log.info("RabbitMQ监听器容器工厂配置完成");
        return factory;
    }

    /**
     * 重试模板
     */
    @Bean
    public RetryTemplate retryTemplate() {
        RetryTemplate retryTemplate = new RetryTemplate();

        // 重试策略
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(rabbitMQProperties.getRetry().getMaxAttempts());
        retryTemplate.setRetryPolicy(retryPolicy);

        // 退避策略
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(rabbitMQProperties.getRetry().getInitialInterval());
        backOffPolicy.setMultiplier(rabbitMQProperties.getRetry().getMultiplier());
        backOffPolicy.setMaxInterval(rabbitMQProperties.getRetry().getMaxInterval());
        retryTemplate.setBackOffPolicy(backOffPolicy);

        return retryTemplate;
    }

    /**
     * 错误处理器
     */
    @Bean
    public ErrorHandler errorHandler() {
        return new ConditionalRejectingErrorHandler(new CustomFatalExceptionStrategy());
    }

    /**
     * 自定义致命异常策略
     */
    public static class CustomFatalExceptionStrategy extends ConditionalRejectingErrorHandler.DefaultExceptionStrategy {

        @Override
        public boolean isFatal(Throwable t) {
            // 可以根据异常类型决定是否为致命异常
            // 致命异常不会重试，直接进入死信队列
            return super.isFatal(t);
        }
    }

    /**
     * 主交换机
     */
    @Bean
    public DirectExchange mainExchange() {
        return new DirectExchange(
                rabbitMQProperties.getExchange().getMain(),
                rabbitMQProperties.getExchange().getDurable(),
                rabbitMQProperties.getExchange().getAutoDelete()
        );
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange deadLetterExchange() {
        return new DirectExchange(
                rabbitMQProperties.getExchange().getDeadLetter(),
                rabbitMQProperties.getExchange().getDurable(),
                rabbitMQProperties.getExchange().getAutoDelete()
        );
    }

    /**
     * 文档处理队列
     */
    @Bean
    public Queue documentProcessingQueue() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-message-ttl", rabbitMQProperties.getQueue().getMessageTtl());
        args.put("x-dead-letter-exchange", rabbitMQProperties.getExchange().getDeadLetter());
        args.put("x-dead-letter-routing-key", "dead.letter");

        return new Queue(
                rabbitMQProperties.getQueue().getDocumentProcessing(),
                rabbitMQProperties.getQueue().getDurable(),
                rabbitMQProperties.getQueue().getExclusive(),
                rabbitMQProperties.getQueue().getAutoDelete(),
                args
        );
    }

    /**
     * 向量化处理队列
     */
    @Bean
    public Queue embeddingProcessingQueue() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-message-ttl", rabbitMQProperties.getQueue().getMessageTtl());
        args.put("x-dead-letter-exchange", rabbitMQProperties.getExchange().getDeadLetter());
        args.put("x-dead-letter-routing-key", "dead.letter");

        return new Queue(
                rabbitMQProperties.getQueue().getEmbeddingProcessing(),
                rabbitMQProperties.getQueue().getDurable(),
                rabbitMQProperties.getQueue().getExclusive(),
                rabbitMQProperties.getQueue().getAutoDelete(),
                args
        );
    }

    /**
     * 文档分段处理队列
     */
    @Bean
    public Queue documentSegmentationQueue() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-message-ttl", rabbitMQProperties.getQueue().getMessageTtl());
        args.put("x-dead-letter-exchange", rabbitMQProperties.getExchange().getDeadLetter());
        args.put("x-dead-letter-routing-key", "dead.letter");

        return new Queue(
                rabbitMQProperties.getQueue().getDocumentSegmentation(),
                rabbitMQProperties.getQueue().getDurable(),
                rabbitMQProperties.getQueue().getExclusive(),
                rabbitMQProperties.getQueue().getAutoDelete(),
                args
        );
    }

    /**
     * 文档状态推送队列
     */
    @Bean
    public Queue documentStatusPushQueue() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-message-ttl", rabbitMQProperties.getQueue().getMessageTtl());
        args.put("x-dead-letter-exchange", rabbitMQProperties.getExchange().getDeadLetter());
        args.put("x-dead-letter-routing-key", "dead.letter");

        return new Queue(
                rabbitMQProperties.getQueue().getDocumentStatusPush(),
                rabbitMQProperties.getQueue().getDurable(),
                rabbitMQProperties.getQueue().getExclusive(),
                rabbitMQProperties.getQueue().getAutoDelete(),
                args
        );
    }

    /**
     * 通知队列
     */
    @Bean
    public Queue notificationQueue() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-message-ttl", rabbitMQProperties.getQueue().getMessageTtl());
        args.put("x-dead-letter-exchange", rabbitMQProperties.getExchange().getDeadLetter());
        args.put("x-dead-letter-routing-key", "dead.letter");

        return new Queue(
                rabbitMQProperties.getQueue().getNotification(),
                rabbitMQProperties.getQueue().getDurable(),
                rabbitMQProperties.getQueue().getExclusive(),
                rabbitMQProperties.getQueue().getAutoDelete(),
                args
        );
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue deadLetterQueue() {
        return new Queue(
                rabbitMQProperties.getQueue().getDeadLetter(),
                rabbitMQProperties.getQueue().getDurable(),
                rabbitMQProperties.getQueue().getExclusive(),
                rabbitMQProperties.getQueue().getAutoDelete()
        );
    }

    /**
     * 文档处理队列绑定
     */
    @Bean
    public Binding documentProcessingBinding() {
        return BindingBuilder
                .bind(documentProcessingQueue())
                .to(mainExchange())
                .with(MessageType.DOCUMENT_PROCESSING.getRoutingKey());
    }

    /**
     * 向量化处理队列绑定
     */
    @Bean
    public Binding embeddingProcessingBinding() {
        return BindingBuilder
                .bind(embeddingProcessingQueue())
                .to(mainExchange())
                .with(MessageType.EMBEDDING_PROCESSING.getRoutingKey());
    }

    /**
     * 文档分段处理队列绑定
     */
    @Bean
    public Binding documentSegmentationBinding() {
        return BindingBuilder
                .bind(documentSegmentationQueue())
                .to(mainExchange())
                .with(MessageType.DOCUMENT_SEGMENTATION.getRoutingKey());
    }

    /**
     * 文档状态推送队列绑定
     */
    @Bean
    public Binding documentStatusPushBinding() {
        return BindingBuilder
                .bind(documentStatusPushQueue())
                .to(mainExchange())
                .with(MessageType.DOCUMENT_STATUS_PUSH.getRoutingKey());
    }

    /**
     * 通知队列绑定
     */
    @Bean
    public Binding notificationBinding() {
        return BindingBuilder
                .bind(notificationQueue())
                .to(mainExchange())
                .with(MessageType.NOTIFICATION.getRoutingKey());
    }

    /**
     * 死信队列绑定
     */
    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder
                .bind(deadLetterQueue())
                .to(deadLetterExchange())
                .with(MessageType.DEAD_LETTER.getRoutingKey());
    }

    /**
     * 消息恢复器
     */
    @Bean
    public MessageRecoverer messageRecoverer(RabbitTemplate rabbitTemplate) {
        return new RepublishMessageRecoverer(rabbitTemplate,
                rabbitMQProperties.getExchange().getDeadLetter(), MessageType.DEAD_LETTER.getRoutingKey());
    }
}
