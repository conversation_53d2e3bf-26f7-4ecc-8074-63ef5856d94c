package com.xhcai.modules.rag.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.DocumentSegment;

/**
 * 文档分段服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IDocumentSegmentService extends IService<DocumentSegment> {

    /**
     * 分页查询文档分段
     *
     * @param current 当前页
     * @param size 页大小
     * @param documentId 文档ID
     * @param datasetId 知识库ID
     * @param status 状态
     * @param enabled 是否启用
     * @return 分页结果
     */
    IPage<DocumentSegment> pageByDocument(Long current, Long size, String documentId,
            String datasetId, String status, Boolean enabled);

    /**
     * 处理文档分段
     *
     * @param documentId 文档ID
     * @return 是否处理成功
     */
    boolean processDocumentSegmentation(String documentId);

    /**
     * 处理文档分段（带用户信息）
     *
     * @param documentId 文档ID
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 是否处理成功
     */
    boolean processDocumentSegmentation(String documentId, String tenantId, String userId);

    /**
     * 下载,预览 文件并进行分段处理
     *
     * @param document 文档信息
     * @param previewUrl 预览URL
     * @return 分段结果列表
     * @throws Exception 处理异常
     */
    List<SegmentResult> downloadAndProcessFile(Document document, String previewUrl) throws Exception;

    /**
     * 根据文档ID查询分段列表
     *
     * @param documentId 文档ID
     * @return 分段列表
     */
    List<DocumentSegment> listByDocumentId(String documentId);

    /**
     * 根据知识库ID查询分段列表
     *
     * @param datasetId 知识库ID
     * @return 分段列表
     */
    List<DocumentSegment> listByDatasetId(String datasetId);

    /**
     * 批量保存文档分段
     *
     * @param segments 分段列表
     * @return 是否成功
     */
    boolean batchSave(List<DocumentSegment> segments);

    /**
     * 更新分段向量
     *
     * @param segmentId 分段ID
     * @param vector 向量数组
     * @return 是否成功
     */
    boolean updateVector(String segmentId, float[] vector);

    /**
     * 更新分段状态
     *
     * @param segmentId 分段ID
     * @param status 状态
     * @param error 错误信息
     * @return 是否成功
     */
    boolean updateStatus(String segmentId, String status, String error);

    /**
     * 批量更新分段状态
     *
     * @param segmentIds 分段ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateStatus(List<String> segmentIds, String status);

    /**
     * 删除文档的所有分段
     *
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteByDocumentId(String documentId);

    /**
     * 删除知识库的所有分段
     *
     * @param datasetId 知识库ID
     * @return 删除数量
     */
    int deleteByDatasetId(String datasetId);

    /**
     * 统计文档分段数量
     *
     * @param documentId 文档ID
     * @return 分段数量
     */
    Long countByDocumentId(String documentId);

    /**
     * 统计知识库分段数量
     *
     * @param datasetId 知识库ID
     * @return 分段数量
     */
    Long countByDatasetId(String datasetId);

    /**
     * 统计已向量化的分段数量
     *
     * @param documentId 文档ID
     * @return 已向量化分段数量
     */
    Long countVectorizedByDocumentId(String documentId);

    /**
     * 获取待向量化的分段列表
     *
     * @param limit 限制数量
     * @return 分段列表
     */
    List<DocumentSegment> getWaitingSegments(Integer limit);

    /**
     * 获取处理中的分段列表
     *
     * @return 分段列表
     */
    List<DocumentSegment> getProcessingSegments();

    /**
     * 搜索相似分段
     *
     * @param queryVector 查询向量
     * @param datasetId 知识库ID
     * @param topK 返回数量
     * @param threshold 相似度阈值
     * @return 相似分段列表
     */
    List<DocumentSegment> searchSimilarSegments(float[] queryVector, String datasetId,
            int topK, double threshold);

    /**
     * 关键词搜索分段
     *
     * @param keyword 关键词
     * @param datasetId 知识库ID
     * @param topK 返回数量
     * @return 匹配分段列表
     */
    List<DocumentSegment> searchByKeyword(String keyword, String datasetId, int topK);

    /**
     * 混合搜索分段（向量 + 关键词）
     *
     * @param queryVector 查询向量
     * @param keyword 关键词
     * @param datasetId 知识库ID
     * @param topK 返回数量
     * @param vectorWeight 向量权重
     * @param keywordWeight 关键词权重
     * @return 搜索结果
     */
    List<DocumentSegment> hybridSearch(float[] queryVector, String keyword, String datasetId,
            int topK, double vectorWeight, double keywordWeight);

    /**
     * 获取分段统计信息
     *
     * @param documentId 文档ID
     * @return 统计信息
     */
    Object getSegmentStats(String documentId);

    /**
     * 重新分段文档
     *
     * @param documentId 文档ID
     * @param chunkSize 分段大小
     * @param chunkOverlap 分段重叠
     * @return 是否成功
     */
    boolean resegmentDocument(String documentId, int chunkSize, int chunkOverlap);
}
