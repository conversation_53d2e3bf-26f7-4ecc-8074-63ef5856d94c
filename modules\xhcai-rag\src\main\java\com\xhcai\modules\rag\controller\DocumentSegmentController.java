package com.xhcai.modules.rag.controller;

import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.rag.dto.SegmentResult;
import com.xhcai.modules.rag.service.IDocumentSegmentService;
import com.xhcai.modules.rag.vo.DocumentPreviewVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文档管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "文档管理", description = "文档分段预览，查询等操作")
@RestController
@RequestMapping("/api/rag/document-segments")
@Validated
public class DocumentSegmentController {
    @Autowired
    private IDocumentSegmentService documentSegmentService;
    @Operation(summary = "预览文档分段", description = "预览文档的分段内容")
    @PostMapping("/preview")
    @RequiresPermissions("rag:document:list")
    private void previewDocumentSegments(@RequestBody DocumentPreviewVO documentPreviewVO) {
        // TODO: 实现文档预览逻辑
//        List<SegmentResult> segmentResults = documentSegmentService.dow
    }
}
