package com.xhcai.modules.dify.service.impl;

import com.alibaba.fastjson2.JSON;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.dify.config.DifyConfig;
import com.xhcai.modules.dify.constant.DifyConstants;
import com.xhcai.modules.dify.dto.app.DifyAppParametersResponseDTO;
import com.xhcai.modules.dify.dto.app.DifyInstalledAppsResponseDTO;
import com.xhcai.modules.dify.service.IDifyAppService;
import com.xhcai.modules.dify.service.IDifyAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;

/**
 * Dify 应用服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class DifyAppServiceImpl implements IDifyAppService {

    private static final Logger log = LoggerFactory.getLogger(DifyAppServiceImpl.class);

    @Autowired
    @Qualifier("difyWebClient")
    private WebClient difyWebClient;

    @Autowired
    private DifyConfig difyConfig;

    @Autowired
    private IDifyAuthService difyAuthService;

    @Override
    public Mono<Result<DifyInstalledAppsResponseDTO>> getInstalledApps() {
        log.info("获取已安装应用列表");

        // 如果启用测试模式，返回Mock响应
        if (difyConfig.isTestMode()) {
            log.info("测试模式已启用，返回Mock已安装应用列表");
            DifyInstalledAppsResponseDTO mockResponse = createMockInstalledApps();
            return Mono.just(Result.success(mockResponse));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用已安装应用列表接口");
                    return difyWebClient.get()
                            .uri(DifyConstants.ApiPath.INSTALLED_APPS)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                            .retrieve()
                            .bodyToMono(String.class)
                            .map(response -> {
                                log.debug("已安装应用列表响应: {}", response);
                                DifyInstalledAppsResponseDTO responseDTO = JSON.parseObject(response, DifyInstalledAppsResponseDTO.class);
                                return Result.success(responseDTO);
                            });
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    @Override
    public Mono<Result<DifyAppParametersResponseDTO>> getAppParameters(String installedAppId) {
        log.info("获取应用会话参数: installedAppId={}", installedAppId);

        if (!StringUtils.hasText(installedAppId)) {
            return Mono.just(Result.fail("已安装应用ID不能为空"));
        }

        // 如果启用测试模式，返回Mock响应
        if (difyConfig.isTestMode()) {
            log.info("测试模式已启用，返回Mock应用会话参数");
            DifyAppParametersResponseDTO mockResponse = createMockAppParameters();
            return Mono.just(Result.success(mockResponse));
        }

        return difyAuthService.getValidAccessToken()
                .flatMap(accessToken -> {
                    log.debug("使用访问令牌调用应用会话参数接口: installedAppId={}", installedAppId);
                    String uri = DifyConstants.ApiPath.APP_PARAMETERS.replace("{installedAppId}", installedAppId);
                    return difyWebClient.get()
                            .uri(uri)
                            .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                            .retrieve()
                            .bodyToMono(String.class)
                            .map(response -> {
                                log.debug("应用会话参数响应: {}", response);
                                DifyAppParametersResponseDTO responseDTO = JSON.parseObject(response, DifyAppParametersResponseDTO.class);
                                return Result.success(responseDTO);
                            });
                })
                .retryWhen(Retry.backoff(difyConfig.getRetryCount(), Duration.ofMillis(difyConfig.getRetryInterval())))
                .onErrorResume(this::handleError);
    }

    /**
     * 创建Mock已安装应用列表
     */
    private DifyInstalledAppsResponseDTO createMockInstalledApps() {
        DifyInstalledAppsResponseDTO response = new DifyInstalledAppsResponseDTO();
        // 这里可以添加Mock数据
        return response;
    }

    /**
     * 创建Mock应用会话参数
     */
    private DifyAppParametersResponseDTO createMockAppParameters() {
        DifyAppParametersResponseDTO response = new DifyAppParametersResponseDTO();
        response.setOpeningStatement("");
        
        // 设置回答后建议问题配置
        DifyAppParametersResponseDTO.SuggestedQuestionsAfterAnswer suggestedQuestionsAfterAnswer = 
            new DifyAppParametersResponseDTO.SuggestedQuestionsAfterAnswer();
        suggestedQuestionsAfterAnswer.setEnabled(false);
        response.setSuggestedQuestionsAfterAnswer(suggestedQuestionsAfterAnswer);

        // 设置语音转文字配置
        DifyAppParametersResponseDTO.SpeechToText speechToText = 
            new DifyAppParametersResponseDTO.SpeechToText();
        speechToText.setEnabled(false);
        response.setSpeechToText(speechToText);

        // 设置文字转语音配置
        DifyAppParametersResponseDTO.TextToSpeech textToSpeech = 
            new DifyAppParametersResponseDTO.TextToSpeech();
        textToSpeech.setEnabled(false);
        textToSpeech.setVoice("");
        textToSpeech.setLanguage("");
        response.setTextToSpeech(textToSpeech);

        // 设置检索资源配置
        DifyAppParametersResponseDTO.RetrieverResource retrieverResource = 
            new DifyAppParametersResponseDTO.RetrieverResource();
        retrieverResource.setEnabled(true);
        response.setRetrieverResource(retrieverResource);

        // 设置注释回复配置
        DifyAppParametersResponseDTO.AnnotationReply annotationReply = 
            new DifyAppParametersResponseDTO.AnnotationReply();
        annotationReply.setEnabled(false);
        response.setAnnotationReply(annotationReply);

        // 设置更多类似配置
        DifyAppParametersResponseDTO.MoreLikeThis moreLikeThis = 
            new DifyAppParametersResponseDTO.MoreLikeThis();
        moreLikeThis.setEnabled(false);
        response.setMoreLikeThis(moreLikeThis);

        // 设置敏感词规避配置
        DifyAppParametersResponseDTO.SensitiveWordAvoidance sensitiveWordAvoidance =
            new DifyAppParametersResponseDTO.SensitiveWordAvoidance();
        sensitiveWordAvoidance.setEnabled(false);
        response.setSensitiveWordAvoidance(sensitiveWordAvoidance);

        // 设置文件上传配置
        DifyAppParametersResponseDTO.FileUpload fileUpload =
            new DifyAppParametersResponseDTO.FileUpload();
        fileUpload.setEnabled(true);
        fileUpload.setAllowedFileTypes(java.util.Arrays.asList("image", "document"));
        fileUpload.setAllowedFileExtensions(java.util.Arrays.asList(".JPG", ".JPEG", ".PNG", ".GIF", ".WEBP", ".SVG"));
        fileUpload.setAllowedFileUploadMethods(java.util.Arrays.asList("remote_url", "local_file"));
        fileUpload.setNumberLimits(3);

        // 设置文件上传配置详情
        DifyAppParametersResponseDTO.FileUploadConfig fileUploadConfig =
            new DifyAppParametersResponseDTO.FileUploadConfig();
        fileUploadConfig.setFileSizeLimit(15);
        fileUploadConfig.setBatchCountLimit(5);
        fileUploadConfig.setImageFileSizeLimit(10);
        fileUploadConfig.setVideoFileSizeLimit(100);
        fileUploadConfig.setAudioFileSizeLimit(50);
        fileUploadConfig.setWorkflowFileUploadLimit(10);
        fileUpload.setFileUploadConfig(fileUploadConfig);
        response.setFileUpload(fileUpload);

        // 设置系统参数
        DifyAppParametersResponseDTO.SystemParameters systemParameters =
            new DifyAppParametersResponseDTO.SystemParameters();
        systemParameters.setImageFileSizeLimit(10);
        systemParameters.setVideoFileSizeLimit(100);
        systemParameters.setAudioFileSizeLimit(50);
        systemParameters.setFileSizeLimit(15);
        systemParameters.setWorkflowFileUploadLimit(10);
        response.setSystemParameters(systemParameters);

        return response;
    }

    /**
     * 处理错误
     */
    private <T> Mono<Result<T>> handleError(Throwable throwable) {
        log.error("Dify应用服务调用失败", throwable);
        
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            HttpStatus status = (HttpStatus) ex.getStatusCode();
            String responseBody = ex.getResponseBodyAsString();
            
            log.error("HTTP错误: status={}, body={}", status, responseBody);
            
            if (status == HttpStatus.UNAUTHORIZED) {
                return Mono.just(Result.fail(status.value(), "认证失败，请检查访问令牌"));
            } else if (status == HttpStatus.NOT_FOUND) {
                return Mono.just(Result.fail(status.value(), "请求的资源不存在"));
            } else if (status == HttpStatus.BAD_REQUEST) {
                return Mono.just(Result.fail(status.value(), "请求参数错误"));
            } else {
                return Mono.just(Result.fail(status.value(), "服务调用失败: " + status.getReasonPhrase()));
            }
        } else if (throwable instanceof BusinessException) {
            return Mono.just(Result.fail(500, throwable.getMessage()));
        } else {
            return Mono.just(Result.fail(500, "服务调用异常: " + throwable.getMessage()));
        }
    }
}
